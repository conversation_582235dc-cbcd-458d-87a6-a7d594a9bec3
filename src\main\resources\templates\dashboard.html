<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title th:text="#{dashboard.success.title}">Authentication Success</title>
    <link rel="stylesheet" th:href="@{/css/base.css}">
    <link rel="stylesheet" th:href="@{/css/alerts.css}">
    <link rel="stylesheet" th:href="@{/css/buttons.css}">
    <link rel="stylesheet" th:href="@{/css/dashboard.css}">
</head>
<body>
    <div class="language-switcher">
        <a th:href="@{/setLanguage(lang='en')}" th:text="#{language.english}">English</a> |
        <a th:href="@{/setLanguage(lang='ru')}" th:text="#{language.russian}">Russian</a>
    </div>

    <div class="container">
        <div th:if="${error}" class="alert alert-danger">
            <p th:text="${error}"></p>
        </div>
        <div th:if="${success}" class="alert alert-success">
            <p th:text="${success}"></p>
        </div>

        <p th:text="#{dashboard.welcome.message}">Welcome to the Task Management System.</p>
        <div th:if="${user != null}">
            <p>
                <span th:text="#{dashboard.hello}">Hello</span>,
                <span th:text="${user.username}">username</span>
                (<span th:text="${user.email}">email</span>)!
            </p>
        </div>

        <div class="mt-30">
            <h2>My Projects</h2>

            <div class="mb-20">
                <a th:href="@{/projects/create}" class="btn btn-primary">
                    Create New Project
                </a>
            </div>
            <div th:if="${projects != null and !projects.isEmpty()}">
                <div class="dashboard-projects-grid">
                    <div th:each="project : ${projects}" class="dashboard-project-card">
                        <h3 th:text="${project.name}" class="dashboard-project-title">Project Name</h3>
                        <p th:text="${project.description}" class="dashboard-project-description">Project Description</p>
                        <div class="dashboard-project-status">
                            <span class="font-bold">Status: </span>
                            <span th:text="${project.status}" th:classappend="'status-' + ${#strings.toLowerCase(project.status)}">Status</span>
                        </div>
                        <div class="dashboard-project-status">
                            <span class="font-bold">Created: </span>
                            <span th:text="${#temporals.format(project.createdAt, 'dd/MM/yyyy HH:mm')}" class="text-muted">Date</span>
                        </div>
                        <div class="dashboard-project-actions">
                            <a th:href="@{/projects/{id}(id=${project.id})}" class="btn btn-primary btn-sm">
                                View
                            </a>
                            <a th:href="@{/projects/{id}/edit(id=${project.id})}" class="btn btn-warning btn-sm">
                                Edit
                            </a>
                            <a th:href="@{/projects/{id}/members(id=${project.id})}" class="btn btn-info btn-sm">
                                Members
                            </a>
                        </div>
                    </div>
                </div>
            </div>


            <div th:if="${projects == null or projects.isEmpty()}" class="empty-state">
                <h3>No Projects Yet</h3>
                <p>You haven't created any projects yet. Click the button above to create your first project!</p>
            </div>
        </div>

        <div class="mt-30 text-center">
            <form th:action="@{/auth/logout}" method="post" style="display: inline;">
                <input type="hidden" th:name="${_csrf.parameterName}" th:value="${_csrf.token}" />
                <button type="submit" th:text="#{nav.logout}" class="btn btn-secondary">Logout</button>
            </form>
        </div>
    </div>
</body>
</html>