<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title th:text="'Project: ' + ${project?.name ?: 'Unknown'}">Project Details</title>
    <link rel="stylesheet" th:href="@{/css/base.css}">
    <link rel="stylesheet" th:href="@{/css/buttons.css}">
    <link rel="stylesheet" th:href="@{/css/alerts.css}">
    <link rel="stylesheet" th:href="@{/css/projects.css}">
    <link rel="stylesheet" th:href="@{/css/members.css}">
</head>
<body>
    <div class="language-switcher">
        <a th:href="@{/setLanguage(lang='en')}" th:text="#{language.english}">English</a> |
        <a th:href="@{/setLanguage(lang='ru')}" th:text="#{language.russian}">Russian</a>
    </div>

    <div class="container container-wide">
        <div th:if="${error}" class="alert alert-danger">
            <p th:text="${error}"></p>
        </div>
        <div th:if="${success}" class="alert alert-success">
            <p th:text="${success}"></p>
        </div>
        <div th:if="${project}" class="project-header">
            <h1 class="project-title" th:text="${project.name}">Project Name</h1>
            <p th:if="${project.description}" th:text="${project.description}" class="text-muted no-margin">Project Description</p>
        </div>


        <div th:unless="${project}" class="alert alert-danger">
            <h2>Project Not Found</h2>
            <p>The requested project could not be found or you don't have permission to view it.</p>
            <a th:href="@{/dashboard}" class="btn btn-secondary">Back to Dashboard</a>
        </div>

        <div th:if="${project}">
            <div class="project-meta">
                <div class="meta-item">
                    <div class="meta-label">Status</div>
                    <div class="meta-value" th:classappend="'status-' + ${#strings.toLowerCase(project.status)}">
                        <span th:text="${project.status}">Status</span>
                    </div>
                </div>
                <div class="meta-item">
                    <div class="meta-label">Created</div>
                    <div class="meta-value" th:text="${#temporals.format(project.createdAt, 'dd/MM/yyyy HH:mm')}">Date</div>
                </div>
                <div class="meta-item" th:if="${project.updatedAt}">
                    <div class="meta-label">Last Updated</div>
                    <div class="meta-value" th:text="${#temporals.format(project.updatedAt, 'dd/MM/yyyy HH:mm')}">Date</div>
                </div>
                <div class="meta-item">
                    <div class="meta-label">Project ID</div>
                    <div class="meta-value" th:text="${project.id}">ID</div>
                </div>
            </div>


            <div class="section">
                <h3 class="section-title">Project Members</h3>
                <div th:if="${members != null and !members.isEmpty()}" class="members-grid">
                    <div th:each="member : ${members}" class="member-card">
                        <div class="member-name" th:text="${member.username ?: member.userId}">Member Name</div>
                        <div class="member-role" th:text="${member.role}">Role</div>
                        <div class="member-role" th:text="'Joined: ' + ${#temporals.format(member.joinedAt, 'dd/MM/yyyy')}">Joined Date</div>
                    </div>
                </div>
                <div th:if="${members == null or members.isEmpty()}" class="empty-state">
                    <p>No members found for this project.</p>
                </div>
            </div>


            <div class="actions-section">
                <h3 class="section-title">Actions</h3>
                <a th:href="@{/projects/{id}/invite(id=${project.id})}" class="btn btn-primary">
                    <i class="fas fa-user-plus"></i> Invite Members
                </a>
                <a th:href="@{/projects/{id}/edit(id=${project.id})}" class="btn btn-warning">Edit Project</a>
                <a th:href="@{/projects/{id}/members(id=${project.id})}" class="btn btn-info">Manage Members</a>
                <a th:href="@{/dashboard}" class="btn btn-secondary">Back to Dashboard</a>
                

                <form th:action="@{/projects/{id}/delete(id=${project.id})}" method="post" style="display: inline;"
                      onsubmit="return confirm('Are you sure you want to delete this project? This action cannot be undone.');">
                    <input type="hidden" th:name="${_csrf.parameterName}" th:value="${_csrf.token}" />
                    <button type="submit" class="btn btn-danger">Delete Project</button>
                </form>
            </div>
        </div>
    </div>
</body>
</html>
