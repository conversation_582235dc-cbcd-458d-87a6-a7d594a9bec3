<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title th:text="#{auth.register.title}">Sign Up</title>
</head>
<body>
    <div style="text-align: right; margin: 10px;">
        <a th:href="@{/setLanguage(lang='en')}" th:text="#{language.english}">English</a> |
        <a th:href="@{/setLanguage(lang='ru')}" th:text="#{language.russian}">Russian</a>
    </div>

    <h1 th:text="#{auth.register.title}">Sign Up</h1>

    <div th:if="${error}">
        <p style="color: red;" th:text="${error}"></p>
    </div>
    <div th:if="${emailError}" style="color: red;">
        <p th:text="${emailError}"></p>
    </div>

    <form th:action="@{/auth/register}" th:object="${user}" method="post">
        <input type="hidden" th:name="${_csrf.parameterName}" th:value="${_csrf.token}" />
        <div>
            <label for="username" th:text="#{auth.username}">Username:</label>
            <input type="text" id="username" th:field="*{username}"
                   th:placeholder="#{auth.username}" />
            <div th:if="${#fields.hasErrors('username')}" th:errors="*{username}" style="color: red;"></div>
        </div>

        <div>
            <label for="email" th:text="#{auth.email}">Email:</label>
            <input type="email" id="email" th:field="*{email}"
                   th:placeholder="#{auth.email}" />
            <div th:if="${#fields.hasErrors('email')}" th:errors="*{email}" style="color: red;"></div>
        </div>

        <div>
            <label for="password" th:text="#{auth.password}">Password:</label>
            <input type="password" id="password" th:field="*{password}"
                   th:placeholder="#{auth.password}" />
            <div th:if="${#fields.hasErrors('password')}" th:errors="*{password}" style="color: red;"></div>
        </div>

        <div>
            <label for="confirmPassword" th:text="#{auth.confirm.password}">Confirm Password:</label>
            <input type="password" id="confirmPassword" th:field="*{confirmPassword}"
                   th:placeholder="#{auth.confirm.password}" />
            <div th:if="${#fields.hasErrors('confirmPassword')}" th:errors="*{confirmPassword}" style="color: red;"></div>
        </div>

        <div th:if="${passwordError}" style="color: red;">
            <p th:text="${passwordError}"></p>
        </div>

        <button type="submit" th:text="#{auth.register.button}">Sign Up</button>
    </form>

    <div>
        <a th:href="@{/}" th:text="#{nav.home}">Return to homepage</a> |
        <a th:href="@{/auth/login}" th:text="#{auth.login}">Login</a>
    </div>
</body>
</html>