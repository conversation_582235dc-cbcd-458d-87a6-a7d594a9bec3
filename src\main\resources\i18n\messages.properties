
# Common
language.english=English
language.russian=Russian

# Navigation
nav.home=Home
nav.logout=Logout

# Homepage
homepage.title=WebUI Service
homepage.welcome=Welcome to Task Helper

# Authentication
auth.login=Login
auth.register=Register
auth.email=Email
auth.password=Password
auth.confirm.password=Confirm Password
auth.username=Username
auth.login.title=Sign In
auth.register.title=Sign Up
auth.login.button=Sign In
auth.register.button=Sign Up

# Dashboard
dashboard.success.title=Authentication Success
dashboard.welcome.message=Welcome to the Task Management System.
dashboard.hello=Hello

# Admin Dashboard
admin.dashboard.title=Admin Dashboard
admin.dashboard.welcome=Welcome to the Admin Dashboard
admin.dashboard.privileges=You have administrator privileges.

# Errors
error.general=An error occurred

# Login errors
error.login.invalid.credentials=Invalid username or password
error.login.account.disabled=Account is disabled
error.login.account.locked=Account is locked

# Registration errors
error.registration.email.exists=Email already exists
error.registration.username.exists=Username already exists
error.registration.passwords.mismatch=Passwords do not match
error.registration.email.invalid=Invalid email format

# Validation errors
error.validation.failed=Validation failed

# Token errors
error.token.invalid=Invalid token
error.token.expired=Token has expired

# General errors
error.user.not.found=User not found
error.rate.limit.exceeded=Too many requests, please try again later
error.server.internal=Internal server error
error.service.unavailable=Service is temporarily unavailable

# Success messages
success.registration=Registration successful

# Registration
registration.check.email=Please check your email to confirm your account.
registration.success.message=Registration successful! Please check your email to confirm.

# Account confirmation
account.confirmed.title=Account Confirmed
account.confirmed.message=Your account has been successfully confirmed!

# Validation messages
validation.username.required=Username or email is required
validation.username.size=Username must be between 3 and 50 characters
validation.email.required=Email is required
validation.email.format=Email must be valid
validation.password.required=Password is required
validation.password.size=Password must be at least 6 characters
validation.confirmPassword.required=Confirm password is required

# Token management
token.expired.title=Token Expired
token.expired.message=Your account verification token has expired.
token.expired.instruction=Please click on the button below to receive a new token.
token.resend.button=Send new token

# Footer
footer.copyright=© 2025 WebUI Service. All rights reserved.
