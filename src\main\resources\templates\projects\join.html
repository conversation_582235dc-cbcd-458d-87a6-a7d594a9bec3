<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Join Project - TaskHelper</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header text-center">
                        <h4><i class="fas fa-handshake me-2"></i>Project Invitation</h4>
                    </div>
                    <div class="card-body">
                        <!-- Success Message -->
                        <div th:if="${success}" class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            <span th:text="${success}"></span>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>

                        <!-- Error Message -->
                        <div th:if="${error}" class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            <span th:text="${error}"></span>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>

                        <!-- Invitation Note -->
                        <div th:if="${invitationNote}" class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <span th:text="${invitationNote}"></span>
                        </div>

                        <!-- Need Authentication -->
                        <div th:if="${needsAuth}" class="text-center">
                            <div class="alert alert-warning">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                You need to log in to accept this invitation.
                            </div>
                            <a href="/login" class="btn btn-primary">
                                <i class="fas fa-sign-in-alt me-1"></i>Login
                            </a>
                            <a href="/register" class="btn btn-outline-primary ms-2">
                                <i class="fas fa-user-plus me-1"></i>Register
                            </a>
                        </div>

                        <!-- Invitation Details -->
                        <div th:if="${invitation and not needsAuth}">
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <div class="card bg-light">
                                        <div class="card-body">
                                            <h5 class="card-title">
                                                <i class="fas fa-project-diagram me-2"></i>
                                                <span th:text="${invitation.projectName}"></span>
                                            </h5>
                                            <p class="card-text">
                                                <strong>Role:</strong> 
                                                <span class="badge bg-primary" th:text="${invitation.role.displayName}"></span>
                                            </p>
                                            <p class="card-text" th:if="${invitation.invitedByUsername}">
                                                <strong>Invited by:</strong> 
                                                <span th:text="${invitation.invitedByUsername}"></span>
                                                <span th:if="${invitation.invitedByEmail}" 
                                                      class="text-muted">
                                                    (<span th:text="${invitation.invitedByEmail}"></span>)
                                                </span>
                                            </p>
                                            <p class="card-text" th:if="${invitation.invitedEmail}">
                                                <strong>Originally intended for:</strong> 
                                                <span th:text="${invitation.invitedEmail}"></span>
                                            </p>
                                            <p class="card-text" th:if="${invitation.acceptedEmail}">
                                                <strong>Accepted by:</strong> 
                                                <span th:text="${invitation.acceptedEmail}"></span>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card bg-light">
                                        <div class="card-body">
                                            <h6 class="card-title">
                                                <i class="fas fa-info-circle me-2"></i>Invitation Details
                                            </h6>
                                            <p class="card-text">
                                                <strong>Status:</strong> 
                                                <span class="badge" 
                                                      th:classappend="${invitation.status.name() == 'PENDING'} ? 'bg-warning' : 
                                                                     ${invitation.status.name() == 'ACCEPTED'} ? 'bg-success' : 'bg-danger'"
                                                      th:text="${invitation.status.displayName}"></span>
                                            </p>
                                            <p class="card-text">
                                                <strong>Created:</strong> 
                                                <span th:text="${#temporals.format(invitation.createdAt, 'dd/MM/yyyy HH:mm')}"></span>
                                            </p>
                                            <p class="card-text">
                                                <strong>Expires:</strong> 
                                                <span th:text="${#temporals.format(invitation.expiresAt, 'dd/MM/yyyy HH:mm')}"></span>
                                            </p>
                                            <p class="card-text" th:if="${invitation.acceptedAt}">
                                                <strong>Accepted:</strong> 
                                                <span th:text="${#temporals.format(invitation.acceptedAt, 'dd/MM/yyyy HH:mm')}"></span>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Personal Message -->
                            <div th:if="${invitation.message}" class="alert alert-light">
                                <h6><i class="fas fa-comment me-2"></i>Personal Message:</h6>
                                <p th:text="${invitation.message}" class="mb-0"></p>
                            </div>

                            <!-- Current User Info -->
                            <div th:if="${user}" class="alert alert-info">
                                <i class="fas fa-user me-2"></i>
                                You are logged in as: <strong th:text="${user.username}"></strong> 
                                (<span th:text="${user.email}"></span>)
                            </div>

                            <!-- Action Buttons -->
                            <div class="text-center" th:if="${invitation.status.name() == 'PENDING'}">
                                <form th:action="@{/join}" method="post" class="d-inline">
                                    <input type="hidden" name="token" th:value="${token}">
                                    <button type="submit" class="btn btn-success btn-lg me-2">
                                        <i class="fas fa-check me-1"></i>Accept Invitation
                                    </button>
                                </form>
                                <a href="/dashboard" class="btn btn-secondary">
                                    <i class="fas fa-times me-1"></i>Decline
                                </a>
                            </div>

                            <!-- Already Accepted -->
                            <div class="text-center" th:if="${invitation.status.name() == 'ACCEPTED'}">
                                <div class="alert alert-success">
                                    <i class="fas fa-check-circle me-2"></i>
                                    This invitation has already been accepted.
                                </div>
                                <a href="/dashboard" class="btn btn-primary">
                                    <i class="fas fa-tachometer-alt me-1"></i>Go to Dashboard
                                </a>
                            </div>

                            <!-- Expired or Cancelled -->
                            <div class="text-center" th:if="${invitation.status.name() == 'EXPIRED' or invitation.status.name() == 'CANCELLED'}">
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    This invitation is no longer valid.
                                </div>
                                <a href="/dashboard" class="btn btn-primary">
                                    <i class="fas fa-tachometer-alt me-1"></i>Go to Dashboard
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
