package by.algin.webuiservice.service;

import by.algin.dto.project.*;
import by.algin.dto.response.PagedResponse;
import by.algin.dto.response.UserResponse;
import by.algin.webuiservice.client.ProjectServiceClient;
import by.algin.webuiservice.client.UserServiceClient;
import by.algin.webuiservice.constants.ModelAttributeConstants;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;
import org.springframework.ui.Model;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
public class ProjectWebService {

    private final ProjectServiceClient projectServiceClient;
    private final UserServiceClient userServiceClient;


    public void prepareDashboardWithProjects(Authentication authentication, Model model) {
        Optional<UserResponse> userOpt = getCurrentUser(authentication);
        if (userOpt.isEmpty()) {
            log.warn("Dashboard accessed without valid user");
            model.addAttribute(ModelAttributeConstants.ERROR_ATTRIBUTE, "User not found");
            return;
        }

        UserResponse user = userOpt.get();
        model.addAttribute(ModelAttributeConstants.USER_ATTRIBUTE, user);

        try {
            log.info("User ID: {}", user.getId());
            log.info("Username: {}", user.getUsername());
            log.info("Email: {}", user.getEmail());

            Optional<PagedResponse<ProjectResponse>> projectsOpt = projectServiceClient.getUserProjects(user.getUsername());
            log.info("ProjectService response received: {}", projectsOpt.isPresent());

            if (projectsOpt.isPresent()) {
                PagedResponse<ProjectResponse> projectsResponse = projectsOpt.get();
                List<ProjectResponse> projects = projectsResponse.getContent();
                log.info("Projects response details:");
                log.info("- Total elements: {}", projectsResponse.getTotalElements());
                log.info("- Total pages: {}", projectsResponse.getTotalPages());
                log.info("- Current page: {}", projectsResponse.getPage());
                log.info("- Page size: {}", projectsResponse.getSize());
                log.info("- Content size: {}", projects != null ? projects.size() : "null");

                if (projects != null && !projects.isEmpty()) {
                    for (int i = 0; i < projects.size(); i++) {
                        ProjectResponse project = projects.get(i);
                        log.info("Project {}: id={}, name={}, ownerId={}, status={}",
                                i + 1, project.getId(), project.getName(), project.getOwnerId(), project.getStatus());
                    }
                    model.addAttribute(ModelAttributeConstants.PROJECTS_ATTRIBUTE, projects);
                } else {
                    model.addAttribute(ModelAttributeConstants.PROJECTS_ATTRIBUTE, Collections.emptyList());
                }
            } else {
                model.addAttribute(ModelAttributeConstants.PROJECTS_ATTRIBUTE, Collections.emptyList());
            }
        } catch (Exception e) {
            log.error("User: {}", user.getUsername());
            log.error("Error message: {}", e.getMessage());
            log.error("Error details: ", e);
            model.addAttribute(ModelAttributeConstants.PROJECTS_ATTRIBUTE, Collections.emptyList());
            model.addAttribute(ModelAttributeConstants.ERROR_ATTRIBUTE, "Project service is currently unavailable");
        }
    }

    public boolean createProject(CreateProjectRequest request, Authentication authentication, Model model) {
        log.info("Authentication: {}", authentication != null ? authentication.getName() : "null");
        log.info("Is authenticated: {}", authentication != null ? authentication.isAuthenticated() : "false");

        Optional<UserResponse> userOpt = getCurrentUser(authentication);
        if (userOpt.isEmpty()) {
            log.error("User not found for authentication: {}", authentication != null ? authentication.getName() : "null");
            model.addAttribute(ModelAttributeConstants.ERROR_ATTRIBUTE, "User not found");
            return false;
        }

        UserResponse user = userOpt.get();
        log.info("User found: id={}, username={}", user.getId(), user.getUsername());

        try {
            log.info("Project name: {}", request.getName());
            log.info("Project description: {}", request.getDescription());
            log.info("User: {}", user.getUsername());

            CreateProjectRequest projectRequest = CreateProjectRequest.builder()
                    .name(request.getName())
                    .description(request.getDescription())
                    .ownerId(user.getId())
                    .status(by.algin.dto.project.ProjectStatus.ACTIVE)
                    .build();

            log.info("Calling ProjectService to create project...");
            Optional<ProjectResponse> projectOpt = projectServiceClient.createProject(projectRequest);
            if (projectOpt.isPresent()) {
                ProjectResponse project = projectOpt.get();
                model.addAttribute(ModelAttributeConstants.SUCCESS_ATTRIBUTE, "Project '" + project.getName() + "' created successfully");
                log.info("=== PROJECT CREATED SUCCESSFULLY: {} ===", project.getName());
                return true;
            } else {
                model.addAttribute(ModelAttributeConstants.ERROR_ATTRIBUTE, "Failed to create project");
                log.error("Failed to create project: {} for user: {}", request.getName(), user.getUsername());
                return false;
            }

        } catch (Exception e) {
            log.error("Error creating project: {} for user: {}, error: {}", request.getName(), user.getUsername(), e.getMessage(), e);
            model.addAttribute(ModelAttributeConstants.ERROR_ATTRIBUTE, "Failed to create project: " + e.getMessage());
            return false;
        }
    }

    public void prepareProjectView(String projectId, Authentication authentication, Model model) {
        Optional<UserResponse> userOpt = getCurrentUser(authentication);
        if (userOpt.isEmpty()) {
            model.addAttribute(ModelAttributeConstants.ERROR_ATTRIBUTE, "User not found");
            return;
        }

        UserResponse user = userOpt.get();
        model.addAttribute(ModelAttributeConstants.USER_ATTRIBUTE, user);

        Optional<ProjectResponse> projectOpt = projectServiceClient.getProject(projectId);
        if (projectOpt.isPresent()) {
            ProjectResponse project = projectOpt.get();
            model.addAttribute(ModelAttributeConstants.PROJECT_ATTRIBUTE, project);
            log.info("Loaded project details: {} for user: {}", project.getName(), user.getUsername());

            Optional<ProjectMemberListResponse> membersOpt = projectServiceClient.getProjectMembers(projectId);
            if (membersOpt.isPresent()) {
                model.addAttribute(ModelAttributeConstants.MEMBERS_ATTRIBUTE, membersOpt.get().getMembers());
                log.info("Loaded {} members for project: {}", membersOpt.get().getMembers().size(), project.getName());
            } else {
                model.addAttribute(ModelAttributeConstants.MEMBERS_ATTRIBUTE, Collections.emptyList());
                log.warn("Failed to load members for project: {}", projectId);
            }
        } else {
            model.addAttribute(ModelAttributeConstants.ERROR_ATTRIBUTE, "Project not found");
            log.error("Project not found: {} for user: {}", projectId, user.getUsername());
        }
    }

    public boolean updateProject(String projectId, UpdateProjectRequest request, Authentication authentication, Model model) {
        Optional<UserResponse> userOpt = getCurrentUser(authentication);
        if (userOpt.isEmpty()) {
            model.addAttribute(ModelAttributeConstants.ERROR_ATTRIBUTE, "User not found");
            return false;
        }

        UserResponse user = userOpt.get();

        Optional<ProjectResponse> projectOpt = projectServiceClient.updateProject(projectId, request);
        if (projectOpt.isPresent()) {
            ProjectResponse project = projectOpt.get();
            model.addAttribute(ModelAttributeConstants.SUCCESS_ATTRIBUTE, "Project '" + project.getName() + "' updated successfully");
            log.info("Project updated successfully: {} by user: {}", project.getName(), user.getUsername());
            return true;
        } else {
            model.addAttribute(ModelAttributeConstants.ERROR_ATTRIBUTE, "Failed to update project");
            log.error("Failed to update project: {} for user: {}", projectId, user.getUsername());
            return false;
        }
    }

    public boolean deleteProject(String projectId, Authentication authentication, Model model) {
        Optional<UserResponse> userOpt = getCurrentUser(authentication);
        if (userOpt.isEmpty()) {
            model.addAttribute(ModelAttributeConstants.ERROR_ATTRIBUTE, "User not found");
            return false;
        }

        UserResponse user = userOpt.get();

        boolean deleted = projectServiceClient.deleteProject(projectId);
        if (deleted) {
            model.addAttribute(ModelAttributeConstants.SUCCESS_ATTRIBUTE, "Project deleted successfully");
            log.info("Project deleted successfully: {} by user: {}", projectId, user.getUsername());
            return true;
        } else {
            model.addAttribute(ModelAttributeConstants.ERROR_ATTRIBUTE, "Failed to delete project");
            log.error("Failed to delete project: {} for user: {}", projectId, user.getUsername());
            return false;
        }
    }

    public boolean addProjectMember(String projectId, AddProjectMemberRequest request, Authentication authentication, Model model) {
        Optional<UserResponse> userOpt = getCurrentUser(authentication);
        if (userOpt.isEmpty()) {
            model.addAttribute(ModelAttributeConstants.ERROR_ATTRIBUTE, "User not found");
            return false;
        }

        UserResponse user = userOpt.get();

        Optional<ProjectMemberResponse> memberOpt = projectServiceClient.addProjectMember(projectId, request);
        if (memberOpt.isPresent()) {
            model.addAttribute(ModelAttributeConstants.SUCCESS_ATTRIBUTE, "Member added successfully");
            log.info("Member added to project: {} by user: {}", projectId, user.getUsername());
            return true;
        } else {
            model.addAttribute(ModelAttributeConstants.ERROR_ATTRIBUTE, "Failed to add member");
            log.error("Failed to add member to project: {} for user: {}", projectId, user.getUsername());
            return false;
        }
    }

    public boolean removeProjectMember(String projectId, String userId, Authentication authentication, Model model) {
        Optional<UserResponse> userOpt = getCurrentUser(authentication);
        if (userOpt.isEmpty()) {
            model.addAttribute(ModelAttributeConstants.ERROR_ATTRIBUTE, "User not found");
            return false;
        }

        UserResponse user = userOpt.get();

        boolean removed = projectServiceClient.removeProjectMember(projectId, userId);
        if (removed) {
            model.addAttribute(ModelAttributeConstants.SUCCESS_ATTRIBUTE, "Member removed successfully");
            log.info("Member removed from project: {} by user: {}", projectId, user.getUsername());
            return true;
        } else {
            model.addAttribute(ModelAttributeConstants.ERROR_ATTRIBUTE, "Failed to remove member");
            log.error("Failed to remove member from project: {} for user: {}", projectId, user.getUsername());
            return false;
        }
    }

    private Optional<UserResponse> getCurrentUser(Authentication authentication) {
        log.info("Authentication: {}", authentication);

        if (!isValidAuthentication(authentication)) {
            log.warn("Authentication is not valid");
            return Optional.empty();
        }

        String username = authentication.getName();
        log.info("Retrieving user info for username: {}", username);

        Optional<UserResponse> userOpt = userServiceClient.searchUsers("username", username);
        if (userOpt.isPresent()) {
            UserResponse user = userOpt.get();
            log.info("Successfully retrieved user: id={}, username={}", user.getId(), user.getUsername());
            return userOpt;
        } else {
            log.error("User not found for username: {}", username);
            return Optional.empty();
        }
    }

    private boolean isValidAuthentication(Authentication authentication) {
        return authentication != null
                && authentication.isAuthenticated()
                && !isAnonymousUser(authentication.getName());
    }

    private boolean isAnonymousUser(String username) {
        return ModelAttributeConstants.ANONYMOUS_USER.equals(username);
    }
}
