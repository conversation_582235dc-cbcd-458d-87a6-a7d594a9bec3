package by.algin.webuiservice.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public final class JsonResponseUtil {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    private JsonResponseUtil() {
    }

    public static void writeErrorResponse(HttpServletResponse response,
                                        String error,
                                        String message,
                                        int status,
                                        String path) throws IOException {
        Map<String, Object> errorResponse = createErrorMap(error, message, status, path);
        writeJsonResponse(response, errorResponse, status);
    }

    public static void writeErrorResponse(HttpServletResponse response,
                                        String error,
                                        String message,
                                        int status) throws IOException {
        Map<String, Object> errorResponse = createErrorMap(error, message, status, null);
        writeJsonResponse(response, errorResponse, status);
    }


    private static Map<String, Object> createErrorMap(String error, String message, int status, String path) {
        Map<String, Object> errorMap = new HashMap<>();
        errorMap.put("error", error);
        errorMap.put("message", message);
        errorMap.put("status", status);
        errorMap.put("timestamp", LocalDateTime.now());
        if (path != null) {
            errorMap.put("path", path);
        }
        return errorMap;
    }

    public static void writeJsonResponse(HttpServletResponse response, 
                                       Object object, 
                                       int status) throws IOException {
        response.setStatus(status);
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        response.setCharacterEncoding("UTF-8");
        
        try {
            String jsonResponse = objectMapper.writeValueAsString(object);
            response.getWriter().write(jsonResponse);
            response.getWriter().flush();
        } catch (Exception e) {
            log.error("Failed to serialize object to JSON: {}", e.getMessage(), e);
            response.getWriter().write("{\"error\":\"Internal Server Error\",\"message\":\"Failed to process request\"}");
            response.getWriter().flush();
        }
    }
}
