.form-group {
    margin-bottom: 20px;
}

.form-inline {
    display: flex;
    gap: 10px;
    align-items: end;
}

.form-inline .form-group {
    flex: 1;
    margin-bottom: 0;
}

label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    color: #333;
}

input[type="text"], 
input[type="email"], 
input[type="password"], 
textarea, 
select {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    box-sizing: border-box;
}

textarea {
    height: 100px;
    resize: vertical;
}

.add-member-form {
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 6px;
    border: 1px solid #dee2e6;
}

.current-value {
    background-color: #f8f9fa;
    padding: 10px;
    border-radius: 4px;
    margin-bottom: 10px;
    border-left: 4px solid #007bff;
}

.current-label {
    font-weight: bold;
    color: #495057;
    margin-bottom: 5px;
}

.current-text {
    color: #6c757d;
}

.error {
    color: #dc3545;
    font-size: 12px;
    margin-top: 5px;
}
