package by.algin.webuiservice.exception;

import by.algin.common.exception.ApiErrorCode;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;

@Getter
@RequiredArgsConstructor
public enum WebUIServiceErrorCode implements ApiErrorCode {

    PAGE_NOT_FOUND("UI_001", "Page not found", HttpStatus.NOT_FOUND, "UI"),
    TEMPLATE_ERROR("UI_002", "Template rendering error", HttpStatus.INTERNAL_SERVER_ERROR, "UI"),
    FORM_VALIDATION_FAILED("UI_003", "Form validation failed", HttpStatus.BAD_REQUEST, "UI"),

    EXTERNAL_SERVICE_ERROR("SVC_003", "External service error", HttpStatus.BAD_GATEWAY, "SERVICE"),
    SERVICE_UNAVAILABLE("SVC_001", "Service unavailable", HttpStatus.SERVICE_UNAVAILABLE, "SERVICE"),
    MICROSERVICE_TIMEOUT("SVC_002", "Service timeout", HttpStatus.REQUEST_TIMEOUT, "SERVICE");


    private final String code;
    private final String defaultMessage;
    private final HttpStatus httpStatus;
    private final String category;
}
