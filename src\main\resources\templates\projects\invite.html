<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invite to Project - TaskHelper</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h4><i class="fas fa-user-plus me-2"></i>Invite to Project</h4>
                    </div>
                    <div class="card-body">
                        <!-- Success Message -->
                        <div th:if="${success}" class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            <span th:text="${success}"></span>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>

                        <!-- Error Message -->
                        <div th:if="${error}" class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            <span th:text="${error}"></span>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>

                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>New Invitation System:</strong> 
                            Create a universal invitation link that anyone can use to join the project. 
                            The email field is optional and only used for your reference.
                        </div>

                        <form th:action="@{/projects/{projectId}/invite(projectId=${projectId})}" 
                              th:object="${invitation}" method="post">
                            
                            <div class="mb-3">
                                <label for="email" class="form-label">
                                    <i class="fas fa-envelope me-1"></i>Email (Optional)
                                </label>
                                <input type="email" 
                                       class="form-control" 
                                       id="email" 
                                       th:field="*{email}"
                                       placeholder="Enter email for reference (optional)">
                                <div class="form-text">
                                    This is optional and only used for logging purposes. 
                                    The invitation link can be used by anyone.
                                </div>
                                <div th:if="${#fields.hasErrors('email')}" 
                                     class="text-danger" 
                                     th:errors="*{email}"></div>
                            </div>

                            <div class="mb-3">
                                <label for="role" class="form-label">
                                    <i class="fas fa-user-tag me-1"></i>Role <span class="text-danger">*</span>
                                </label>
                                <select class="form-select" 
                                        id="role" 
                                        th:field="*{role}" 
                                        required>
                                    <option value="">Select a role</option>
                                    <option th:each="role : ${roles}" 
                                            th:value="${role}" 
                                            th:text="${role.displayName}"></option>
                                </select>
                                <div th:if="${#fields.hasErrors('role')}" 
                                     class="text-danger" 
                                     th:errors="*{role}"></div>
                            </div>

                            <div class="mb-3">
                                <label for="message" class="form-label">
                                    <i class="fas fa-comment me-1"></i>Message (Optional)
                                </label>
                                <textarea class="form-control" 
                                          id="message" 
                                          th:field="*{message}" 
                                          rows="3" 
                                          placeholder="Add a personal message to the invitation..."></textarea>
                                <div th:if="${#fields.hasErrors('message')}" 
                                     class="text-danger" 
                                     th:errors="*{message}"></div>
                            </div>

                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a th:href="@{/projects/{projectId}(projectId=${projectId})}" 
                                   class="btn btn-secondary me-md-2">
                                    <i class="fas fa-arrow-left me-1"></i>Cancel
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-paper-plane me-1"></i>Create Invitation Link
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
