package by.algin.webuiservice.config;

import feign.RequestInterceptor;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@RequiredArgsConstructor
public class ProjectServiceFeignConfig {

    private final FeignJwtInterceptor feignJwtInterceptor;

    @Bean
    public RequestInterceptor projectServiceRequestInterceptor() {
        return feignJwtInterceptor;
    }
}
