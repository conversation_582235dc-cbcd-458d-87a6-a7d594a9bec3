package by.algin.webuiservice.constants;

public final class MessageConstants {
    
    public static final String PAGE_NOT_FOUND = "Page not found";
    public static final String TEMPLATE_NOT_FOUND = "Template not found";
    public static final String FORM_VALIDATION_FAILED = "Form validation failed";
    public static final String SESSION_EXPIRED = "Session expired";
    public static final String ACCESS_DENIED_UI = "Access denied";
    public static final String LOGIN_FAILED = "Login failed";
    public static final String LOGOUT_SUCCESSFUL = "Logout successful";
    public static final String LOGOUT_FAILED = "Logout failed";
    public static final String REGISTRATION_FAILED = "Registration failed";
    public static final String PROFILE_UPDATE_FAILED = "Profile update failed";
    public static final String PASSWORD_CHANGE_FAILED = "Password change failed";
    public static final String EMAIL_CONFIRMATION_FAILED = "Email confirmation failed";
    
    public static final String INTERNAL_SERVER_ERROR = "Internal server error";
    public static final String BAD_REQUEST = "Bad request";
    public static final String UNAUTHORIZED = "Unauthorized";
    public static final String FORBIDDEN = "Forbidden";
    public static final String NOT_FOUND = "Not found";
    public static final String METHOD_NOT_ALLOWED = "Method not allowed";
    public static final String REQUEST_TIMEOUT = "Request timeout";
    public static final String TOO_MANY_REQUESTS = "Too many requests";
    
    public static final String USER_SERVICE_UNAVAILABLE = "User service unavailable";
    public static final String EXTERNAL_SERVICE_ERROR = "External service error";
    public static final String MICROSERVICE_COMMUNICATION_FAILED = "Microservice communication failed";

    private MessageConstants() {}
}
