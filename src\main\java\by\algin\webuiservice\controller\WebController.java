package by.algin.webuiservice.controller;

import by.algin.webuiservice.constants.PathConstants;

import by.algin.webuiservice.service.ProjectWebService;
import by.algin.webuiservice.service.UserDashboardService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;

@Slf4j
@Controller
@RequiredArgsConstructor
public class WebController {

    private final UserDashboardService userDashboardService;
    private final ProjectWebService projectWebService;

    @GetMapping(PathConstants.ROOT)
    public String home() {
        return PathConstants.TEMPLATE_INDEX;
    }

    @GetMapping(PathConstants.DASHBOARD)
    public String dashboard(Authentication authentication, Model model) {
        try {
            userDashboardService.prepareDashboard(authentication, model);
            projectWebService.prepareDashboardWithProjects(authentication, model);
        } catch (Exception e) {
            log.error("Error preparing dashboard: ", e);
            model.addAttribute("error", "Error loading dashboard data");
        }
        return PathConstants.TEMPLATE_DASHBOARD;
    }

    @GetMapping(PathConstants.ADMIN_DASHBOARD)
    @PreAuthorize("hasAuthority('ADMIN')")
    public String adminDashboard(Authentication authentication, Model model) {
        userDashboardService.prepareAdminDashboard(authentication, model);
        return PathConstants.TEMPLATE_ADMIN_DASHBOARD;
    }
}