<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title th:text="#{token.expired.title}">Token Expired</title>
</head>
<body>
    <div style="text-align: right; margin: 10px;">
        <a th:href="@{/setLanguage(lang='en')}" th:text="#{language.english}">English</a> |
        <a th:href="@{/setLanguage(lang='ru')}" th:text="#{language.russian}">Russian</a>
    </div>

    <h1 th:text="#{token.expired.message}">Your account verification token has expired.</h1>
    <p th:text="#{token.expired.instruction}">Please click on the button below to receive a new token.</p>

    <div th:if="${email != null and !#strings.isEmpty(email)}">
        <p>A new confirmation email will be sent to: <strong th:text="${email}"></strong></p>
        <form th:action="@{/auth/resend-confirmation}" method="post">
            <input type="hidden" th:value="${email}" name="email"/>
            <input type="hidden" th:value="${token}" name="token" th:if="${token != null}"/>
            <button type="submit" th:text="#{token.resend.button}">Send new token</button>
        </form>
    </div>

    <div th:if="${email == null or #strings.isEmpty(email)}">
        <p>Could not determine your email address. Please contact support.</p>
    </div>

    <div style="margin-top: 20px;">
        <a th:href="@{/}" th:text="#{nav.home}">Return to homepage</a>
    </div>
</body>
</html>