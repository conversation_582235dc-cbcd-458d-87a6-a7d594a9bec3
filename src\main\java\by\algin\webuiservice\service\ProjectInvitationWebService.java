package by.algin.webuiservice.service;

import by.algin.dto.project.CreateInvitationRequest;
import by.algin.dto.project.InvitationResponse;
import by.algin.dto.project.ProjectMemberResponse;
import by.algin.dto.response.UserResponse;
import by.algin.webuiservice.client.ProjectServiceClient;
import by.algin.webuiservice.client.UserServiceClient;
import by.algin.webuiservice.constants.ModelAttributeConstants;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;
import org.springframework.ui.Model;

import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
public class ProjectInvitationWebService {

    private final ProjectServiceClient projectServiceClient;
    private final UserServiceClient userServiceClient;
    
    public boolean sendInvitation(String projectId, CreateInvitationRequest request, 
                                Authentication authentication, Model model) {
        
        Optional<UserResponse> userOpt = getCurrentUser(authentication);
        if (userOpt.isEmpty()) {
            log.error("User not found for authentication: {}", authentication != null ? authentication.getName() : "null");
            model.addAttribute(ModelAttributeConstants.ERROR_ATTRIBUTE, "User not found");
            return false;
        }
        
        try {
            log.info("Sending invitation for project {} to email {}", projectId, request.getEmail());
            
            Optional<InvitationResponse> invitationOpt = projectServiceClient.createInvitation(
                    Long.parseLong(projectId), request);
            
            if (invitationOpt.isPresent()) {
                InvitationResponse invitation = invitationOpt.get();
                log.info("Successfully created invitation with ID {}", invitation.getId());
                String successMessage = request.getEmail() != null && !request.getEmail().trim().isEmpty()
                    ? "Invitation created for " + request.getEmail() + ". Share this link: " + invitation.getInvitationUrl()
                    : "Invitation link created. Share this link: " + invitation.getInvitationUrl();
                model.addAttribute(ModelAttributeConstants.SUCCESS_ATTRIBUTE, successMessage);
                return true;
            } else {
                model.addAttribute(ModelAttributeConstants.ERROR_ATTRIBUTE, "Failed to send invitation");
                return false;
            }
            
        } catch (Exception e) {
            log.error("Error sending invitation for project {}: {}", projectId, e.getMessage(), e);
            model.addAttribute(ModelAttributeConstants.ERROR_ATTRIBUTE, "Failed to send invitation: " + e.getMessage());
            return false;
        }
    }
 
    public void prepareJoinForm(String token, Authentication authentication, Model model) {
        try {
            log.info("Preparing join form for token: {}", token);
            
            Optional<InvitationResponse> invitationOpt = projectServiceClient.getInvitationByToken(token);
            if (invitationOpt.isPresent()) {
                InvitationResponse invitation = invitationOpt.get();
                model.addAttribute(ModelAttributeConstants.INVITATION_ATTRIBUTE, invitation);
                model.addAttribute("token", token);

                if (authentication != null && authentication.isAuthenticated()) {
                    Optional<UserResponse> userOpt = getCurrentUser(authentication);
                    if (userOpt.isPresent()) {
                        UserResponse user = userOpt.get();
                        model.addAttribute(ModelAttributeConstants.USER_ATTRIBUTE, user);

                        if (invitation.getInvitedEmail() != null && !invitation.getInvitedEmail().trim().isEmpty()) {
                            if (!invitation.getInvitedEmail().equalsIgnoreCase(user.getEmail())) {
                                model.addAttribute("invitationNote",
                                        "This invitation was originally intended for " + invitation.getInvitedEmail() +
                                        ", but anyone can use this link to join the project.");
                            }
                        }
                    }
                } else {
                    model.addAttribute("needsAuth", true);
                }
                
                log.info("Prepared join form for project: {}", invitation.getProjectName());
            } else {
                model.addAttribute(ModelAttributeConstants.ERROR_ATTRIBUTE, "Invalid or expired invitation");
            }

        } catch (Exception e) {
            log.error("Error preparing join form for token {}: {}", token, e.getMessage(), e);
            model.addAttribute(ModelAttributeConstants.ERROR_ATTRIBUTE, "Failed to load invitation details");
        }
    }

    public boolean acceptInvitation(String token, Authentication authentication, Model model) {
        Optional<UserResponse> userOpt = getCurrentUser(authentication);
        if (userOpt.isEmpty()) {
            log.error("User not found for authentication: {}", authentication != null ? authentication.getName() : "null");
            model.addAttribute(ModelAttributeConstants.ERROR_ATTRIBUTE, "Please log in to accept the invitation");
            return false;
        }
        
        try {
            log.info("Accepting invitation with token: {}", token);
            
            Optional<ProjectMemberResponse> memberOpt = projectServiceClient.acceptInvitation(token);
            if (memberOpt.isPresent()) {
                ProjectMemberResponse member = memberOpt.get();
                log.info("Successfully accepted invitation and joined project {}", member.getProjectId());
                model.addAttribute(ModelAttributeConstants.SUCCESS_ATTRIBUTE,
                        "Successfully joined the project as " + member.getRole());
                return true;
            } else {
                model.addAttribute(ModelAttributeConstants.ERROR_ATTRIBUTE, "Failed to accept invitation");
                return false;
            }
            
        } catch (Exception e) {
            log.error("Error accepting invitation with token {}: {}", token, e.getMessage(), e);
            model.addAttribute(ModelAttributeConstants.ERROR_ATTRIBUTE, "Failed to accept invitation: " + e.getMessage());
            return false;
        }
    }

    private Optional<UserResponse> getCurrentUser(Authentication authentication) {
        if (authentication == null || !authentication.isAuthenticated() || 
            "anonymousUser".equals(authentication.getName())) {
            return Optional.empty();
        }
        
        String username = authentication.getName();
        return userServiceClient.searchUsers("username", username);
    }
}
