package by.algin.webuiservice.controller;

import by.algin.webuiservice.constants.LanguageConstants;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.LocaleResolver;

import java.util.Locale;

import static by.algin.webuiservice.constants.PathConstants.ROOT;
import static by.algin.webuiservice.constants.PathConstants.SET_LANGUAGE;

@Slf4j
@Controller
@RequiredArgsConstructor
public class LanguageController {

    private final LocaleResolver localeResolver;

    @GetMapping(SET_LANGUAGE)
    public String setLanguage(@RequestParam("lang") String lang, 
                            HttpServletRequest request, 
                            HttpServletResponse response) {
        
        if (!isValidLanguage(lang)) {
            log.warn("Invalid language parameter: {}", lang);
            lang = LanguageConstants.DEFAULT_LANGUAGE;
        }
        
        Locale locale = Locale.forLanguageTag(lang);
        localeResolver.setLocale(request, response, locale);

        log.debug("Language changed to: {} (Locale: {})", lang, locale);

        Locale currentLocale = localeResolver.resolveLocale(request);
        log.debug("Current locale after setting: {}", currentLocale);
        
        String referer = request.getHeader("Referer");
        if (referer != null && !referer.isEmpty() && isInternalUrl(referer, request)) {
            return "redirect:" + referer;
        }

        return "redirect:" + ROOT;
    }

    private boolean isValidLanguage(String lang) {
        return LanguageConstants.ENGLISH.equals(lang) ||
               LanguageConstants.RUSSIAN.equals(lang);
    }

    private boolean isInternalUrl(String url, HttpServletRequest request) {
        try {
            String serverName = request.getServerName();
            int serverPort = request.getServerPort();
            String expectedHost = serverName + (serverPort != 80 && serverPort != 443 ? ":" + serverPort : "");

            return url.startsWith(request.getScheme() + "://" + expectedHost + request.getContextPath()) ||
                   url.startsWith(request.getContextPath()) ||
                   url.startsWith("/");
        } catch (Exception e) {
            log.warn("Error validating internal URL: {}", e.getMessage());
            return false;
        }
    }
}
