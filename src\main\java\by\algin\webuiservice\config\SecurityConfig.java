package by.algin.webuiservice.config;

import by.algin.webuiservice.security.JwtAuthenticationFilter;
import by.algin.webuiservice.util.ErrorResponseHandler;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

import org.springframework.security.core.AuthenticationException;
import org.springframework.security.access.AccessDeniedException;

import java.io.IOException;

import by.algin.constants.CommonPathConstants;
import by.algin.constants.CommonRoleConstants;
import by.algin.constants.CommonTemplateConstants;
import by.algin.webuiservice.constants.PathConstants;

@Slf4j
@Configuration
@EnableWebSecurity
@EnableMethodSecurity
@RequiredArgsConstructor
public class SecurityConfig {

    private final JwtAuthenticationFilter jwtAuthenticationFilter;
    private final ErrorResponseHandler errorResponseHandler;
    private final AppProperties appProperties;

    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        http
                .csrf(csrf -> csrf
                        .csrfTokenRepository(org.springframework.security.web.csrf.CookieCsrfTokenRepository.withHttpOnlyFalse())
                        .ignoringRequestMatchers(CommonPathConstants.API_PATH + "/**", CommonPathConstants.AUTH_RESEND_CONFIRMATION)
                )
                .authorizeHttpRequests(authorize -> authorize
                        .requestMatchers(
                            CommonTemplateConstants.CSS_RESOURCES,
                            CommonTemplateConstants.JS_RESOURCES,
                            CommonPathConstants.AUTH_LOGIN,
                            CommonPathConstants.AUTH_REGISTER,
                            CommonPathConstants.AUTH_REGISTRATION_SUCCESS,
                            CommonPathConstants.AUTH_CONFIRM + "/**",
                            CommonPathConstants.AUTH_TOKEN_EXPIRED,
                            CommonPathConstants.AUTH_RESEND_CONFIRMATION,
                            CommonPathConstants.AUTH_ACCOUNT_CONFIRMED,
                            PathConstants.ROOT,
                            PathConstants.SET_LANGUAGE
                        ).permitAll()
                        .requestMatchers(PathConstants.DASHBOARD).authenticated()
                        .requestMatchers(PathConstants.ADMIN_DASHBOARD).hasRole(CommonRoleConstants.ADMIN)
                        .requestMatchers(CommonPathConstants.PROJECTS_ENDPOINT + "/**").authenticated()
                        .anyRequest().authenticated()
                )
                .logout(logout -> logout
                        .logoutUrl(CommonPathConstants.AUTH_LOGOUT)
                        .logoutSuccessUrl(PathConstants.ROOT)
                        .deleteCookies(
                            appProperties.getSecurity().getCookies().getJwtCookieName(),
                            appProperties.getSecurity().getCookies().getRefreshJwtCookieName(),
                            appProperties.getSecurity().getCookies().getJsessionIdCookieName()
                        )
                        .logoutRequestMatcher(request ->
                            request.getRequestURI().equals(CommonPathConstants.AUTH_LOGOUT) &&
                            "POST".equals(request.getMethod())
                        )
                        .permitAll()
                )
                .addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class)
                .exceptionHandling(exceptions -> exceptions
                        .authenticationEntryPoint(this::handleAuthenticationException)
                        .accessDeniedHandler(this::handleAccessDeniedException)
                )
                .sessionManagement(session -> session
                        .sessionCreationPolicy(SessionCreationPolicy.IF_REQUIRED)
                        .maximumSessions(1)
                );

        return http.build();
    }

    private void handleAuthenticationException(HttpServletRequest request,
                                             HttpServletResponse response,
                                             AuthenticationException authException) throws IOException {
        log.debug("Authentication exception for path: {}, exception: {}", request.getRequestURI(), authException.getMessage());
        errorResponseHandler.handleAuthenticationError(
            request,
            response,
            "JWT token is missing or invalid",
            CommonPathConstants.AUTH_LOGIN
        );
    }

    private void handleAccessDeniedException(HttpServletRequest request,
                                           HttpServletResponse response,
                                           AccessDeniedException accessDeniedException) throws IOException {
        errorResponseHandler.handleAccessDeniedError(
            request,
            response,
            "Access denied",
            PathConstants.ROOT + "?error=access-denied"
        );
    }
}