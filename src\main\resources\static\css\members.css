.members-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 15px;
    margin-bottom: 30px;
}

.members-grid.wide {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.member-card {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 6px;
    border: 1px solid #dee2e6;
    position: relative;
}

.member-card.detailed {
    padding: 20px;
}

.member-name {
    font-weight: bold;
    color: #495057;
    margin-bottom: 5px;
}

.member-name.large {
    font-size: 16px;
}

.member-role {
    color: #6c757d;
    font-size: 14px;
    margin-bottom: 5px;
}

.member-joined {
    color: #6c757d;
    font-size: 14px;
    margin-bottom: 15px;
}

.member-actions {
    margin-top: 15px;
}

.role-owner { color: #dc3545; font-weight: bold; }
.role-manager { color: #fd7e14; font-weight: bold; }
.role-developer { color: #28a745; }
.role-viewer { color: #6c757d; }
