package by.algin.webuiservice.exception;

import by.algin.common.FeignErrorDecoder;
import by.algin.common.exception.InvalidCredentialsException;
import by.algin.common.exception.ValidationException;
import by.algin.constants.CommonPathConstants;
import feign.FeignException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.NoHandlerFoundException;

import jakarta.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@ControllerAdvice
public class WebUiGlobalExceptionHandler {

    private boolean isApiRequest(HttpServletRequest request) {
        String acceptHeader = request.getHeader("Accept");
        String contentType = request.getContentType();
        String requestURI = request.getRequestURI();

        return (acceptHeader != null && acceptHeader.contains("application/json")) ||
               (contentType != null && contentType.contains("application/json")) ||
               requestURI.startsWith(CommonPathConstants.API_PATH);
    }


    @ExceptionHandler(NoHandlerFoundException.class)
    public Object handlePageNotFound(NoHandlerFoundException ex, HttpServletRequest request) {
        log.warn("Page not found: {}", request.getRequestURI());
        if (isApiRequest(request)) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", "PAGE_NOT_FOUND");
            errorResponse.put("message", "Page not found");
            errorResponse.put("status", 404);
            return ResponseEntity.status(404).body(errorResponse);
        } else {
            ModelAndView modelAndView = new ModelAndView("error/404");
            modelAndView.addObject("error", "Page not found");
            modelAndView.addObject("message", "The requested page was not found");
            return modelAndView;
        }
    }

    @ExceptionHandler(FeignErrorDecoder.ServiceCallException.class)
    public Object handleServiceCallException(FeignErrorDecoder.ServiceCallException ex, HttpServletRequest request) {
        log.error("Service call failed: HTTP {} - {}", ex.getHttpStatus(), ex.getMessage());
        if (isApiRequest(request)) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", "SERVICE_CALL_FAILED");
            errorResponse.put("message", ex.getMessage());
            errorResponse.put("status", ex.getHttpStatus());
            return ResponseEntity.status(ex.getHttpStatus()).body(errorResponse);
        } else {
            ModelAndView modelAndView = new ModelAndView("error/500");
            modelAndView.addObject("error", "Service call failed");
            modelAndView.addObject("message", ex.getMessage());
            return modelAndView;
        }
    }

    @ExceptionHandler(FeignException.class)
    public Object handleFeignException(FeignException ex, HttpServletRequest request) {
        log.error("Feign communication error: HTTP {} - {}", ex.status(), ex.getMessage());
        if (isApiRequest(request)) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", "FEIGN_COMMUNICATION_ERROR");
            errorResponse.put("message", "Service communication failed");
            errorResponse.put("status", ex.status());
            return ResponseEntity.status(ex.status()).body(errorResponse);
        } else {
            ModelAndView modelAndView = new ModelAndView("error/500");
            modelAndView.addObject("error", "Service communication error");
            modelAndView.addObject("message", "Failed to communicate with external service");
            return modelAndView;
        }
    }

    @ExceptionHandler(InvalidCredentialsException.class)
    public Object handleInvalidCredentials(InvalidCredentialsException ex, HttpServletRequest request) {
        log.warn("Invalid credentials: {}", ex.getMessage());
        if (isApiRequest(request)) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", "INVALID_CREDENTIALS");
            errorResponse.put("message", ex.getMessage());
            errorResponse.put("status", 401);
            return ResponseEntity.status(401).body(errorResponse);
        } else {
            ModelAndView modelAndView = new ModelAndView("error/401");
            modelAndView.addObject("error", "Invalid credentials");
            modelAndView.addObject("message", ex.getMessage());
            return modelAndView;
        }
    }

    @ExceptionHandler(ValidationException.class)
    public Object handleValidation(ValidationException ex, HttpServletRequest request) {
        log.warn("Validation failed: {}", ex.getMessage());
        if (isApiRequest(request)) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", "VALIDATION_FAILED");
            errorResponse.put("message", ex.getMessage());
            errorResponse.put("status", 400);
            if (ex.getErrors() != null && !ex.getErrors().isEmpty()) {
                errorResponse.put("errors", ex.getErrors());
            }
            return ResponseEntity.status(400).body(errorResponse);
        } else {
            ModelAndView modelAndView = new ModelAndView("error/400");
            modelAndView.addObject("error", "Validation failed");
            modelAndView.addObject("message", ex.getMessage());
            if (ex.getErrors() != null && !ex.getErrors().isEmpty()) {
                modelAndView.addObject("errors", ex.getErrors());
            }
            return modelAndView;
        }
    }

    @ExceptionHandler(Exception.class)
    public Object handleGenericException(Exception ex, HttpServletRequest request) {
        log.error("Unhandled exception: {} at {}", ex.getMessage(), request.getRequestURI(), ex);

        if (isApiRequest(request)) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", "INTERNAL_SERVER_ERROR");
            errorResponse.put("message", "An unexpected error occurred");
            errorResponse.put("status", 500);
            return ResponseEntity.status(500).body(errorResponse);
        } else {
            ModelAndView modelAndView = new ModelAndView("error/500");
            modelAndView.addObject("error", "Internal Server Error");
            modelAndView.addObject("message", "An unexpected error occurred");
            return modelAndView;
        }
    }
}
