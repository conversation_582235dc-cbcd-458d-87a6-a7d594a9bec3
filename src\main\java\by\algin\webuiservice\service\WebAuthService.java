package by.algin.webuiservice.service;

import by.algin.dto.request.RegisterRequest;
import by.algin.dto.response.ApiResponse;
import by.algin.dto.response.AuthResponse;
import by.algin.dto.response.UserResponse;
import by.algin.webuiservice.client.AuthServiceClient;
import by.algin.webuiservice.constants.PathConstants;
import by.algin.webuiservice.constants.RoleConstants;
import feign.FeignException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.security.web.context.HttpSessionSecurityContextRepository;
import org.springframework.stereotype.Service;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class WebAuthService {

    private final AuthServiceClient authServiceClient;
    private final CookieService cookieService;

    public void processSuccessfulLogin(AuthResponse authResponse, HttpServletRequest request, HttpServletResponse response) {
        if (authResponse == null || authResponse.getUsername() == null) {
            log.error("Invalid AuthResponse: username is null");
            throw new IllegalArgumentException("Authentication response is invalid");
        }

        cookieService.createJwtCookies(authResponse, response);
        setSecurityContext(authResponse, request);

        log.info("Successful login processed for user: {} with roles: {}", authResponse.getUsername(), authResponse.getRoles());
    }

    public void clearAuthenticationCookies(HttpServletResponse response) {
        cookieService.clearAuthenticationCookies(response);
        SecurityContextHolder.clearContext();
        log.info("Authentication cookies cleared and security context cleared");
    }

    public String getRegistrationErrorMessage(Exception e) {
        if (e instanceof FeignException feignException) {
            log.error("Registration failed with status {}: {}", feignException.status(), feignException.getMessage());
            if (feignException.status() == 500) {
                return "Registration successful, but there was an issue sending the confirmation email. Please check your email or contact the administrator.";
            }
            return "Registration error: " + feignException.getMessage();
        }

        log.error("Unexpected registration error: {}", e.getMessage(), e);
        return "An unexpected error occurred: " + e.getMessage();
    }

    public boolean hasAdminRole(Set<String> roles) {
        return roles != null && roles.contains(RoleConstants.ADMIN);
    }



    private void setSecurityContext(AuthResponse authResponse, HttpServletRequest request) {
        Set<String> roles = getRolesOrDefault(authResponse);
        List<SimpleGrantedAuthority> authorities = createAuthorities(roles);

        UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(
            authResponse.getUsername(),
            null,
            authorities
        );
        authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));

        SecurityContextHolder.getContext().setAuthentication(authentication);
        
        HttpSession session = request.getSession(true);
        session.setAttribute(HttpSessionSecurityContextRepository.SPRING_SECURITY_CONTEXT_KEY, SecurityContextHolder.getContext());
        
        log.debug("Security context set for user: {} with authorities: {}", authResponse.getUsername(), authorities);
    }

    private Set<String> getRolesOrDefault(AuthResponse authResponse) {
        Set<String> roles = authResponse.getRoles();
        if (roles == null || roles.isEmpty()) {
            log.warn("No roles found for user: {}, assigning default USER role", authResponse.getUsername());
            return Set.of(RoleConstants.USER);
        }
        return roles;
    }

    private List<SimpleGrantedAuthority> createAuthorities(Set<String> roles) {
        return roles.stream()
                .map(SimpleGrantedAuthority::new)
                .collect(Collectors.toList());
    }

    public String processRegistration(RegisterRequest user, BindingResult result, Model model, RedirectAttributes redirectAttributes) {
        if (result.hasErrors()) {
            return PathConstants.TEMPLATE_REGISTER;
        }

        try {
            log.info("Processing registration for user: {}", user.getUsername());
            ApiResponse<UserResponse> response = authServiceClient.registerUser(user);
            
            if (!response.isSuccess()) {
                log.warn("Registration failed for user {}: {}", user.getUsername(), response.getMessage());
                model.addAttribute("error", response.getMessage());
                model.addAttribute("user", user);
                return PathConstants.TEMPLATE_REGISTER;
            }

            log.info("Registration successful for user: {}", user.getUsername());
            return "redirect:" + PathConstants.AUTH_REGISTRATION_SUCCESS;
        } catch (Exception e) {
            return handleRegistrationError(e, model, user);
        }
    }

    public String processAccountConfirmation(String token, RedirectAttributes redirectAttributes) {
        try {
            ApiResponse<String> response = authServiceClient.confirmAccount(token);
            if (response == null || !response.isSuccess()) {
                log.warn("Account confirmation failed for token: {}, response: {}", token, response);

                try {
                    ApiResponse<String> emailResponse = authServiceClient.getEmailByToken(token);
                    if (emailResponse != null && emailResponse.isSuccess() && emailResponse.getData() != null) {
                        String email = emailResponse.getData();
                        log.info("Found email for expired token: {}", email);
                        redirectAttributes.addAttribute("email", email);
                        redirectAttributes.addAttribute("token", token);
                    }
                } catch (Exception emailEx) {
                    log.warn("Could not get email for token: {}", emailEx.getMessage());
                    redirectAttributes.addAttribute("token", token);
                }

                redirectAttributes.addFlashAttribute("error", response != null ? response.getMessage() : "Account confirmation failed");
                return "redirect:" + PathConstants.AUTH_TOKEN_EXPIRED;
            }

            redirectAttributes.addFlashAttribute("success", "Account confirmed successfully! You can now log in.");
            return "redirect:" + PathConstants.AUTH_ACCOUNT_CONFIRMED;
        } catch (Exception e) {
            log.error("Account confirmation failed for token: {}", token, e);

            try {
                ApiResponse<String> emailResponse = authServiceClient.getEmailByToken(token);
                if (emailResponse != null && emailResponse.isSuccess() && emailResponse.getData() != null) {
                    String email = emailResponse.getData();
                    log.info("Found email for expired token after exception: {}", email);
                    redirectAttributes.addAttribute("email", email);
                }
            } catch (Exception emailEx) {
                log.warn("Could not get email for token after exception: {}", emailEx.getMessage());
            }

            redirectAttributes.addAttribute("token", token);
            redirectAttributes.addFlashAttribute("error", "Account confirmation failed. The token may be invalid or expired.");
            return "redirect:" + PathConstants.AUTH_TOKEN_EXPIRED;
        }
    }

    public String processResendConfirmation(String email, RedirectAttributes redirectAttributes) {
        try {
            ApiResponse<String> response = authServiceClient.resendConfirmation(email);
            if (response == null || !response.isSuccess()) {
                log.warn("Failed to resend confirmation for email: {}, response: {}", email, response);
                redirectAttributes.addFlashAttribute("error", response != null ? response.getMessage() : "Failed to resend confirmation email");
                return "redirect:" + PathConstants.AUTH_TOKEN_EXPIRED + "?email=" + URLEncoder.encode(email, StandardCharsets.UTF_8);
            }

            redirectAttributes.addFlashAttribute("success", "Confirmation email resent successfully to " + email);
            redirectAttributes.addAttribute("email", email);
            return "redirect:" + PathConstants.AUTH_TOKEN_RESENT;
        } catch (Exception e) {
            log.error("Failed to resend confirmation for email: {}", email, e);
            redirectAttributes.addFlashAttribute("error", "Failed to resend confirmation email.");
            return "redirect:" + PathConstants.AUTH_TOKEN_EXPIRED + "?email=" + URLEncoder.encode(email, StandardCharsets.UTF_8);
        }
    }

    public String processResendConfirmationByToken(String token, RedirectAttributes redirectAttributes) {
        try {
            // Сначала получаем email по токену
            ApiResponse<String> emailResponse = authServiceClient.getEmailByToken(token);
            if (emailResponse == null || !emailResponse.isSuccess() || emailResponse.getData() == null) {
                log.warn("Could not get email for token: {}", token);
                redirectAttributes.addFlashAttribute("error", "Could not determine email for resending confirmation");
                return "redirect:" + PathConstants.AUTH_TOKEN_EXPIRED + "?token=" + URLEncoder.encode(token, StandardCharsets.UTF_8);
            }

            String email = emailResponse.getData();
            log.info("Found email for token: {}", email);

            // Теперь отправляем письмо подтверждения
            return processResendConfirmation(email, redirectAttributes);

        } catch (Exception e) {
            log.error("Failed to resend confirmation by token: {}", token, e);
            redirectAttributes.addFlashAttribute("error", "Failed to resend confirmation email.");
            return "redirect:" + PathConstants.AUTH_TOKEN_EXPIRED + "?token=" + URLEncoder.encode(token, StandardCharsets.UTF_8);
        }
    }

    private String handleRegistrationError(Exception e, Model model, RegisterRequest user) {
        log.error("Registration failed for user {}: {}", user.getUsername(), e.getMessage());
        String errorMessage = getRegistrationErrorMessage(e);
        model.addAttribute("error", errorMessage);
        model.addAttribute("user", user);
        return PathConstants.TEMPLATE_REGISTER;
    }
}