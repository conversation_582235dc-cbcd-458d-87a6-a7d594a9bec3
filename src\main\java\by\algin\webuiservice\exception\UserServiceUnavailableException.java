package by.algin.webuiservice.exception;

import by.algin.webuiservice.constants.MessageConstants;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(HttpStatus.SERVICE_UNAVAILABLE)
public class UserServiceUnavailableException extends RuntimeException {
    public UserServiceUnavailableException() {
        super(MessageConstants.USER_SERVICE_UNAVAILABLE);
    }

    public UserServiceUnavailableException(String message) {
        super(message);
    }
}
