package by.algin.webuiservice.config;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;

@Component
@Slf4j
public class FeignJwtInterceptor implements RequestInterceptor {

    @Override
    public void apply(RequestTemplate template) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

        if (authentication != null && authentication.isAuthenticated() &&
            !"anonymousUser".equals(authentication.getName())) {

            try {
                String token = extractJwtFromCurrentRequest();
                if (token != null && !token.isEmpty()) {
                    template.header("Authorization", "Bearer " + token);
                    log.debug("Added JWT token to Feign request for user: {}", authentication.getName());
                } else {
                    log.warn("No JWT token available for user: {}", authentication.getName());
                }
            } catch (Exception e) {
                log.error("Failed to add JWT token to Feign request for user: {}, error: {}",
                         authentication.getName(), e.getMessage());
            }
        } else {
            log.debug("No authenticated user found, skipping JWT token addition");
        }
    }

    private String extractJwtFromCurrentRequest() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();

                String authHeader = request.getHeader("Authorization");
                if (authHeader != null && authHeader.startsWith("Bearer ")) {
                    return authHeader.substring(7);
                }

                jakarta.servlet.http.Cookie[] cookies = request.getCookies();
                if (cookies != null) {
                    for (jakarta.servlet.http.Cookie cookie : cookies) {
                        if ("jwt".equals(cookie.getName())) {
                            return cookie.getValue();
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("Error extracting JWT token from current request: {}", e.getMessage());
        }
        return null;
    }
}
