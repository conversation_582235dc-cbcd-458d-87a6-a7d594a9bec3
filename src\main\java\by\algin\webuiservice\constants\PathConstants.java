package by.algin.webuiservice.constants;

public final class PathConstants {

    public static final String ROOT = "/";
    public static final String SET_LANGUAGE = "/setLanguage";

    public static final String DASHBOARD = "/dashboard";
    public static final String ADMIN_DASHBOARD = "/admin-dashboard";

    public static final String AUTH = "/auth";

    public static final String ERROR = "/error";

    // WebUI-specific endpoints that redirect to UserService
    public static final String TOKEN_EXPIRED_ENDPOINT = "/token-expired";
    public static final String ACCOUNT_CONFIRMED = "/account-confirmed";
    public static final String AUTH_TOKEN_RESENT = "/auth/token-resent";
    public static final String REGISTRATION_SUCCESS_ENDPOINT = "/registration-success";

    public static final String TEMPLATE_INDEX = "index";
    public static final String TEMPLATE_LOGIN = "login";
    public static final String TEMPLATE_REGISTER = "register";
    public static final String TEMPLATE_REGISTRATION_SUCCESS = "registration-success";
    // Use UserService for token expired template
    public static final String TEMPLATE_DASHBOARD = "dashboard";
    public static final String TEMPLATE_ADMIN_DASHBOARD = "admin-dashboard";
    public static final String TEMPLATE_ACCOUNT_CONFIRMED = "account-confirmed";
    // Use CommonTemplateConstants.TEMPLATE_ERROR directly

    // Project template names (WebUI specific)
    public static final String TEMPLATE_PROJECTS_CREATE = "projects/create";
    public static final String TEMPLATE_PROJECTS_VIEW = "projects/view";
    public static final String TEMPLATE_PROJECTS_EDIT = "projects/edit";
    public static final String TEMPLATE_PROJECTS_MEMBERS = "projects/members";

    // Redirect paths (WebUI specific)
    public static final String REDIRECT_PROJECTS_VIEW = "redirect:/projects/";
    public static final String REDIRECT_PROJECTS_MEMBERS = "/members";

    // Context ID for project service (WebUI specific)
    public static final String PROJECT_SERVICE_CONTEXT_ID = "project-service-webui";



    private PathConstants() {
    }
}
