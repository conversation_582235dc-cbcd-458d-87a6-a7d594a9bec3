package by.algin.webuiservice.constants;

public final class PathConstants {

    // Base paths (WebUI specific)
    public static final String ROOT = "/";
    public static final String SET_LANGUAGE = "/setLanguage";

    // Dashboard paths (WebUI specific)
    public static final String DASHBOARD = "/dashboard";
    public static final String ADMIN_DASHBOARD = "/admin-dashboard";

    // Project paths (WebUI specific)
    public static final String PROJECTS = "/projects";

    // Auth paths (WebUI specific)
    public static final String AUTH = "/auth";

    // Endpoint suffixes (WebUI specific)
    public static final String TOKEN_EXPIRED_ENDPOINT = "/token-expired";
    public static final String ACCOUNT_CONFIRMED = "/account-confirmed";
    public static final String AUTH_TOKEN_RESENT = "/auth/token-resent";

    // Template names (WebUI specific)
    public static final String TEMPLATE_INDEX = "index";
    public static final String TEMPLATE_LOGIN = "login";
    public static final String TEMPLATE_REGISTER = "register";
    public static final String TEMPLATE_REGISTRATION_SUCCESS = "registration-success";
    public static final String TEMPLATE_TOKEN_EXPIRED = "token-expired";
    public static final String TEMPLATE_DASHBOARD = "dashboard";
    public static final String TEMPLATE_ADMIN_DASHBOARD = "admin-dashboard";
    public static final String TEMPLATE_ACCOUNT_CONFIRMED = "account-confirmed";

    // Endpoint names (WebUI specific - for backward compatibility)
    public static final String REGISTER_ENDPOINT = "/register";
    public static final String LOGIN_ENDPOINT = "/login";
    public static final String LOGOUT_ENDPOINT = "/logout";
    public static final String RESEND_CONFIRMATION_ENDPOINT = "/resend-confirmation";

    private PathConstants() {
    }
}
