package by.algin.webuiservice.controller;

import by.algin.constants.CommonPathConstants;
import by.algin.dto.request.LoginRequest;
import by.algin.dto.request.RegisterRequest;
import by.algin.dto.response.ApiResponse;
import by.algin.dto.response.AuthResponse;
import by.algin.webuiservice.client.AuthServiceClient;
import by.algin.webuiservice.constants.ErrorCodeConstants;
import by.algin.webuiservice.constants.PathConstants;
import by.algin.common.exception.InvalidCredentialsException;
import by.algin.common.exception.ValidationException;
import by.algin.webuiservice.service.WebAuthService;
import by.algin.webuiservice.util.ErrorMessageMapper;
import by.algin.common.FeignErrorDecoder;
import feign.FeignException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.LocaleResolver;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import java.util.Locale;

@Slf4j
@Controller
@RequestMapping(PathConstants.AUTH)
@RequiredArgsConstructor
public class WebAuthController {

    private final AuthServiceClient authServiceClient;
    private final WebAuthService webAuthService;
    private final ErrorMessageMapper errorMessageMapper;
    private final LocaleResolver localeResolver;

    @GetMapping("")
    public String home() {
        return PathConstants.TEMPLATE_INDEX;
    }

    @GetMapping(CommonPathConstants.REGISTER_ENDPOINT)
    public String showRegistrationForm(Model model) {
        model.addAttribute("user", new RegisterRequest());
        return PathConstants.TEMPLATE_REGISTER;
    }

    @PostMapping(CommonPathConstants.REGISTER_ENDPOINT)
    public String register(@Valid @ModelAttribute("user") RegisterRequest user,
                           BindingResult result,
                           Model model,
                           RedirectAttributes redirectAttributes) {
        return webAuthService.processRegistration(user, result, model, redirectAttributes);
    }

    @GetMapping(CommonPathConstants.LOGIN_ENDPOINT)
    public String showLoginForm(@RequestParam(value = "error", required = false) String error,
                               Model model, HttpServletRequest request) {
        if (error != null) {
            Locale currentLocale = localeResolver.resolveLocale(request);
            String localizedMessage = errorMessageMapper.getLocalizedErrorMessage(ErrorCodeConstants.INVALID_CREDENTIALS, currentLocale);
            model.addAttribute("loginError", localizedMessage);
        }
        model.addAttribute("loginRequest", new LoginRequest());
        return PathConstants.TEMPLATE_LOGIN;
    }

    @PostMapping(CommonPathConstants.LOGIN_ENDPOINT)
    public String login(@Valid @ModelAttribute LoginRequest loginRequest,
                        BindingResult result,
                        HttpServletRequest request,
                        HttpServletResponse response,
                        Model model,
                        RedirectAttributes redirectAttributes) {
        if (result.hasErrors()) {
            log.error("Login validation errors: {}", result.getAllErrors());
            throw new ValidationException("Login form validation failed: " + result.getAllErrors().toString());
        }

        log.info("Processing login request: {}", loginRequest);
        try {
            ApiResponse<AuthResponse> apiResponse = authServiceClient.loginUser(loginRequest);
            log.info("Received response from user-service: {}", apiResponse);

            if (!apiResponse.isSuccess()) {
                handleLoginError(apiResponse, result, request);
                return PathConstants.TEMPLATE_LOGIN;
            }

            if (apiResponse.getData() == null) {
                handleUnexpectedError(result, request);
                return PathConstants.TEMPLATE_LOGIN;
            }

            AuthResponse authResponse = apiResponse.getData();
            log.info("Login successful, processing response: {}", authResponse);
            webAuthService.processSuccessfulLogin(authResponse, request, response);
            log.info("Login successful for user: {}", loginRequest.getUsernameOrEmail());
            return determineRedirectUrl(authResponse);

        } catch (FeignErrorDecoder.ServiceCallException e) {
            handleServiceCallError(e, result, request);
            return PathConstants.TEMPLATE_LOGIN;
        } catch (FeignException e) {
            handleServiceError(e, result, request);
            return PathConstants.TEMPLATE_LOGIN;
        } catch (Exception e) {
            log.error("Unexpected error during login for user {}: {}", loginRequest.getUsernameOrEmail(), e.getMessage(), e);
            handleUnexpectedError(result, request);
            return PathConstants.TEMPLATE_LOGIN;
        }
    }

    @GetMapping(PathConstants.REGISTRATION_SUCCESS_ENDPOINT)
    public String registrationSuccess(Model model, HttpServletRequest request) {
        return PathConstants.TEMPLATE_REGISTRATION_SUCCESS;
    }

    @GetMapping(CommonPathConstants.CONFIRM_ENDPOINT + "/{token}")
    public String confirmAccount(@PathVariable("token") String token,
                                 RedirectAttributes redirectAttributes) {
        log.info("Received account confirmation request with token: {}", token);

        if (token == null || token.trim().isEmpty() || token.length() < 10 || token.length() > 500) {
            log.warn("Invalid token format received for account confirmation: length={}", token != null ? token.length() : 0);
            redirectAttributes.addFlashAttribute("error", "Invalid confirmation token");
            return "redirect:" + CommonPathConstants.AUTH_TOKEN_EXPIRED;
        }

        log.info("Processing account confirmation for token: {}", token.trim());
        return webAuthService.processAccountConfirmation(token.trim(), redirectAttributes);
    }

    @GetMapping(PathConstants.ACCOUNT_CONFIRMED)
    public String showAccountConfirmed(Model model) {
        return PathConstants.TEMPLATE_ACCOUNT_CONFIRMED;
    }

    // Redirect to UserService for token expired page
    @GetMapping(PathConstants.TOKEN_EXPIRED_ENDPOINT)
    public String redirectToTokenExpired(@RequestParam(value = "email", required = false) String email,
                                        @RequestParam(value = "token", required = false) String token) {
        StringBuilder redirectUrl = new StringBuilder("redirect:" + CommonPathConstants.AUTH_TOKEN_EXPIRED);

        if (email != null && !email.trim().isEmpty()) {
            redirectUrl.append("?email=").append(email);
            if (token != null && !token.trim().isEmpty()) {
                redirectUrl.append("&token=").append(token);
            }
        } else if (token != null && !token.trim().isEmpty()) {
            redirectUrl.append("?token=").append(token);
        }

        log.info("Redirecting to UserService token expired page");
        return redirectUrl.toString();
    }

    // Redirect to UserService for resend confirmation
    @PostMapping(CommonPathConstants.RESEND_CONFIRMATION_ENDPOINT)
    public String redirectToResendConfirmation(@RequestParam(value = "email", required = false) String email,
                                             @RequestParam(value = "token", required = false) String token) {
        log.info("Redirecting resend confirmation request to UserService - email: {}, token: {}",
                email, token != null ? "present" : "null");

        // Redirect to UserService with parameters
        StringBuilder redirectUrl = new StringBuilder("redirect:" + CommonPathConstants.AUTH_RESEND_CONFIRMATION);

        if (email != null && !email.trim().isEmpty()) {
            redirectUrl.append("?email=").append(email);
        } else if (token != null && !token.trim().isEmpty()) {
            redirectUrl.append("?token=").append(token);
        }

        return redirectUrl.toString();
    }

    // Redirect to UserService for token resent page
    @GetMapping(PathConstants.AUTH_TOKEN_RESENT)
    public String redirectToTokenResent(@RequestParam(value = "email", required = false) String email) {
        StringBuilder redirectUrl = new StringBuilder("redirect:" + CommonPathConstants.AUTH_RESEND_CONFIRMATION);

        if (email != null && !email.trim().isEmpty()) {
            redirectUrl.append("?email=").append(email);
        }

        log.info("Redirecting to UserService token resent page");
        return redirectUrl.toString();
    }



    @PostMapping(CommonPathConstants.LOGOUT_ENDPOINT)
    public String logout(HttpServletResponse response, RedirectAttributes redirectAttributes) {
        log.info("Processing logout request");
        webAuthService.clearAuthenticationCookies(response);
        redirectAttributes.addFlashAttribute("success", "You have successfully logged out");
        log.info("Logout completed successfully");
        return "redirect:" + PathConstants.ROOT;
    }

    private void handleLoginError(ApiResponse<AuthResponse> apiResponse, BindingResult result, HttpServletRequest request) {
        Locale currentLocale = localeResolver.resolveLocale(request);
        String errorCode = apiResponse.getErrorCode();
        String errorMessage = errorMessageMapper.getLocalizedErrorMessage(errorCode, currentLocale);

        log.warn("Login failed with errorCode: {}, message: {}", errorCode, apiResponse.getMessage());
        result.rejectValue("usernameOrEmail", errorCode != null ? errorCode : ErrorCodeConstants.INVALID_CREDENTIALS, errorMessage);
    }

    private void handleServiceCallError(FeignErrorDecoder.ServiceCallException e, BindingResult result, HttpServletRequest request) {
        int httpStatus = e.getHttpStatus();
        String message = e.getMessage();

        if (httpStatus == 401 || httpStatus == 403) {
            log.warn("Authentication failed: HTTP {} - {}", httpStatus, message);
            throw new InvalidCredentialsException("Authentication failed: " + message);
        }

        log.error("Service error: HTTP {} - {}", httpStatus, message, e);
        handleServiceErrorCommon(httpStatus, result, request);
    }

    private void handleServiceError(FeignException e, BindingResult result, HttpServletRequest request) {
        int httpStatus = e.status();

        if (httpStatus == 401 || httpStatus == 403) {
            log.warn("Authentication failed: HTTP {}", httpStatus);
        } else {
            log.error("Service error: HTTP {} - {}", httpStatus, e.getMessage(), e);
        }

        handleServiceErrorCommon(httpStatus, result, request);
    }

    private void handleServiceErrorCommon(int httpStatus, BindingResult result, HttpServletRequest request) {
        Locale currentLocale = localeResolver.resolveLocale(request);
        String errorCode = (httpStatus == 401 || httpStatus == 403)
            ? ErrorCodeConstants.INVALID_CREDENTIALS
            : ErrorCodeConstants.SERVICE_UNAVAILABLE;

        String errorMessage = errorMessageMapper.getLocalizedErrorMessage(errorCode, currentLocale);
        result.rejectValue("usernameOrEmail", errorCode, errorMessage);
    }

    private void handleUnexpectedError(BindingResult result, HttpServletRequest request) {
        Locale currentLocale = localeResolver.resolveLocale(request);
        String errorMessage = errorMessageMapper.getLocalizedErrorMessage(ErrorCodeConstants.INTERNAL_SERVER_ERROR, currentLocale);
        result.rejectValue("usernameOrEmail", ErrorCodeConstants.INTERNAL_SERVER_ERROR, errorMessage);
    }

    private String determineRedirectUrl(AuthResponse authResponse) {
        boolean isAdmin = webAuthService.hasAdminRole(authResponse.getRoles());
        log.info("Redirecting user {} to {} (isAdmin: {}, roles: {})", authResponse.getUsername(),
                isAdmin ? PathConstants.ADMIN_DASHBOARD : PathConstants.DASHBOARD, isAdmin, authResponse.getRoles());
        return isAdmin ? "redirect:" + PathConstants.ADMIN_DASHBOARD : "redirect:" + PathConstants.DASHBOARD;
    }
}