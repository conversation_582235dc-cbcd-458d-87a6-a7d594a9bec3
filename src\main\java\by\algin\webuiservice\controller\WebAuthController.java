package by.algin.webuiservice.controller;

import by.algin.constants.CommonPathConstants;
import by.algin.dto.request.LoginRequest;
import by.algin.dto.request.RegisterRequest;
import by.algin.dto.response.ApiResponse;
import by.algin.dto.response.AuthResponse;
import by.algin.webuiservice.client.AuthServiceClient;
import by.algin.webuiservice.constants.ErrorCodeConstants;
import by.algin.webuiservice.constants.PathConstants;
import by.algin.common.exception.InvalidCredentialsException;
import by.algin.common.exception.ValidationException;
import by.algin.webuiservice.service.WebAuthService;
import by.algin.webuiservice.util.ErrorMessageMapper;
import by.algin.common.FeignErrorDecoder;
import feign.FeignException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.LocaleResolver;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import java.util.Locale;

@Slf4j
@Controller
@RequestMapping(PathConstants.AUTH)
@RequiredArgsConstructor
public class WebAuthController {

    private final AuthServiceClient authServiceClient;
    private final WebAuthService webAuthService;
    private final ErrorMessageMapper errorMessageMapper;
    private final LocaleResolver localeResolver;

    @GetMapping("")
    public String home() {
        return PathConstants.TEMPLATE_INDEX;
    }

    @GetMapping(CommonPathConstants.REGISTER_ENDPOINT)
    public String showRegistrationForm(Model model) {
        model.addAttribute("user", new RegisterRequest());
        return PathConstants.TEMPLATE_REGISTER;
    }

    @PostMapping(CommonPathConstants.REGISTER_ENDPOINT)
    public String register(@Valid @ModelAttribute("user") RegisterRequest user,
                           BindingResult result,
                           Model model,
                           RedirectAttributes redirectAttributes) {
        return webAuthService.processRegistration(user, result, model, redirectAttributes);
    }

    @GetMapping(CommonPathConstants.LOGIN_ENDPOINT)
    public String showLoginForm(@RequestParam(value = "error", required = false) String error,
                               Model model, HttpServletRequest request) {
        if (error != null) {
            Locale currentLocale = localeResolver.resolveLocale(request);
            String localizedMessage = errorMessageMapper.getLocalizedErrorMessage(ErrorCodeConstants.INVALID_CREDENTIALS, currentLocale);
            model.addAttribute("loginError", localizedMessage);
        }
        model.addAttribute("loginRequest", new LoginRequest());
        return PathConstants.TEMPLATE_LOGIN;
    }

    @PostMapping(CommonPathConstants.LOGIN_ENDPOINT)
    public String login(@Valid @ModelAttribute LoginRequest loginRequest,
                        BindingResult result,
                        HttpServletRequest request,
                        HttpServletResponse response,
                        Model model,
                        RedirectAttributes redirectAttributes) {
        if (result.hasErrors()) {
            log.error("Login validation errors: {}", result.getAllErrors());
            throw new ValidationException("Login form validation failed: " + result.getAllErrors().toString());
        }

        log.info("Processing login request: {}", loginRequest);
        try {
            ApiResponse<AuthResponse> apiResponse = authServiceClient.loginUser(loginRequest);
            log.info("Received response from user-service: {}", apiResponse);

            if (!apiResponse.isSuccess()) {
                handleLoginError(apiResponse, result, request);
                return PathConstants.TEMPLATE_LOGIN;
            }

            if (apiResponse.getData() == null) {
                handleUnexpectedError(result, request);
                return PathConstants.TEMPLATE_LOGIN;
            }

            AuthResponse authResponse = apiResponse.getData();
            log.info("Login successful, processing response: {}", authResponse);
            webAuthService.processSuccessfulLogin(authResponse, request, response);
            log.info("Login successful for user: {}", loginRequest.getUsernameOrEmail());
            return determineRedirectUrl(authResponse);

        } catch (FeignErrorDecoder.ServiceCallException e) {
            handleServiceCallError(e, result, request);
            return PathConstants.TEMPLATE_LOGIN;
        } catch (FeignException e) {
            handleServiceError(e, result, request);
            return PathConstants.TEMPLATE_LOGIN;
        } catch (Exception e) {
            log.error("Unexpected error during login for user {}: {}", loginRequest.getUsernameOrEmail(), e.getMessage(), e);
            handleUnexpectedError(result, request);
            return PathConstants.TEMPLATE_LOGIN;
        }
    }

    @GetMapping(PathConstants.REGISTRATION_SUCCESS_ENDPOINT)
    public String registrationSuccess(Model model, HttpServletRequest request) {
        return PathConstants.TEMPLATE_REGISTRATION_SUCCESS;
    }

    @GetMapping(CommonPathConstants.CONFIRM_ENDPOINT + "/{token}")
    public String confirmAccount(@PathVariable("token") String token,
                                 RedirectAttributes redirectAttributes) {
        log.info("Received account confirmation request with token: {}", token);

        if (token == null || token.trim().isEmpty() || token.length() < 10 || token.length() > 500) {
            log.warn("Invalid token format received for account confirmation: length={}", token != null ? token.length() : 0);
            redirectAttributes.addFlashAttribute("error", "Invalid confirmation token");
            return "redirect:" + CommonPathConstants.AUTH_TOKEN_EXPIRED;
        }

        log.info("Processing account confirmation for token: {}", token.trim());
        return webAuthService.processAccountConfirmation(token.trim(), redirectAttributes);
    }

    @GetMapping(PathConstants.ACCOUNT_CONFIRMED)
    public String showAccountConfirmed(Model model) {
        return PathConstants.TEMPLATE_ACCOUNT_CONFIRMED;
    }

    @GetMapping(PathConstants.TOKEN_EXPIRED_ENDPOINT)
    public String showTokenExpired(@RequestParam(value = "email", required = false) String email,
                                  @RequestParam(value = "token", required = false) String token,
                                  Model model) {
        if (email != null && !email.trim().isEmpty()) {
            model.addAttribute("email", email);
            log.info("Showing token expired page with email: {}", email);
        }

        if (token != null && !token.trim().isEmpty()) {
            model.addAttribute("token", token);
            log.info("Showing token expired page with token");

            // Если email не передан, но есть токен - попытаемся получить email по токену
            if (email == null || email.trim().isEmpty()) {
                try {
                    ApiResponse<String> emailResponse = authServiceClient.getEmailByToken(token);
                    if (emailResponse != null && emailResponse.isSuccess() && emailResponse.getData() != null) {
                        String foundEmail = emailResponse.getData();
                        model.addAttribute("email", foundEmail);
                        log.info("Found email for token: {}", foundEmail);
                    }
                } catch (Exception e) {
                    log.warn("Could not get email for token: {}", e.getMessage());
                }
            }
        }

        return PathConstants.TEMPLATE_TOKEN_EXPIRED;
    }

    @PostMapping(CommonPathConstants.RESEND_CONFIRMATION_ENDPOINT)
    public String resendConfirmation(@RequestParam(value = "email", required = false) String email,
                                   @RequestParam(value = "token", required = false) String token,
                                   RedirectAttributes redirectAttributes) {
        log.info("Received resend confirmation request - email: {}, token: {}", email, token != null ? "present" : "null");

        // Если передан email, используем его
        if (email != null && !email.trim().isEmpty()) {
            return webAuthService.processResendConfirmation(email, redirectAttributes);
        }

        // Если передан токен, но нет email - получаем email по токену
        if (token != null && !token.trim().isEmpty()) {
            return webAuthService.processResendConfirmationByToken(token, redirectAttributes);
        }

        // Если ни email, ни токен не переданы
        log.warn("Resend confirmation request without email or token");
        redirectAttributes.addFlashAttribute("error", "Email or token is required for resending confirmation");
        return "redirect:" + CommonPathConstants.AUTH_TOKEN_EXPIRED;
    }

    @GetMapping(PathConstants.AUTH_TOKEN_RESENT)
    public String showTokenResent(@RequestParam(value = "email", required = false) String email, Model model) {
        if (email != null && !email.trim().isEmpty()) {
            model.addAttribute("email", email);
        }
        return "token-resent";
    }



    @PostMapping(CommonPathConstants.LOGOUT_ENDPOINT)
    public String logout(HttpServletResponse response, RedirectAttributes redirectAttributes) {
        log.info("Processing logout request");
        webAuthService.clearAuthenticationCookies(response);
        redirectAttributes.addFlashAttribute("success", "You have successfully logged out");
        log.info("Logout completed successfully");
        return "redirect:" + PathConstants.ROOT;
    }

    private void handleLoginError(ApiResponse<AuthResponse> apiResponse, BindingResult result, HttpServletRequest request) {
        Locale currentLocale = localeResolver.resolveLocale(request);
        String errorCode = apiResponse.getErrorCode();
        String errorMessage = errorMessageMapper.getLocalizedErrorMessage(errorCode, currentLocale);

        log.warn("Login failed with errorCode: {}, message: {}", errorCode, apiResponse.getMessage());
        result.rejectValue("usernameOrEmail", errorCode != null ? errorCode : ErrorCodeConstants.INVALID_CREDENTIALS, errorMessage);
    }

    private void handleServiceCallError(FeignErrorDecoder.ServiceCallException e, BindingResult result, HttpServletRequest request) {
        int httpStatus = e.getHttpStatus();
        String message = e.getMessage();

        if (httpStatus == 401 || httpStatus == 403) {
            log.warn("Authentication failed: HTTP {} - {}", httpStatus, message);
            throw new InvalidCredentialsException("Authentication failed: " + message);
        }

        log.error("Service error: HTTP {} - {}", httpStatus, message, e);
        handleServiceErrorCommon(httpStatus, result, request);
    }

    private void handleServiceError(FeignException e, BindingResult result, HttpServletRequest request) {
        int httpStatus = e.status();

        if (httpStatus == 401 || httpStatus == 403) {
            log.warn("Authentication failed: HTTP {}", httpStatus);
        } else {
            log.error("Service error: HTTP {} - {}", httpStatus, e.getMessage(), e);
        }

        handleServiceErrorCommon(httpStatus, result, request);
    }

    private void handleServiceErrorCommon(int httpStatus, BindingResult result, HttpServletRequest request) {
        Locale currentLocale = localeResolver.resolveLocale(request);
        String errorCode = (httpStatus == 401 || httpStatus == 403)
            ? ErrorCodeConstants.INVALID_CREDENTIALS
            : ErrorCodeConstants.SERVICE_UNAVAILABLE;

        String errorMessage = errorMessageMapper.getLocalizedErrorMessage(errorCode, currentLocale);
        result.rejectValue("usernameOrEmail", errorCode, errorMessage);
    }

    private void handleUnexpectedError(BindingResult result, HttpServletRequest request) {
        Locale currentLocale = localeResolver.resolveLocale(request);
        String errorMessage = errorMessageMapper.getLocalizedErrorMessage(ErrorCodeConstants.INTERNAL_SERVER_ERROR, currentLocale);
        result.rejectValue("usernameOrEmail", ErrorCodeConstants.INTERNAL_SERVER_ERROR, errorMessage);
    }

    private String determineRedirectUrl(AuthResponse authResponse) {
        boolean isAdmin = webAuthService.hasAdminRole(authResponse.getRoles());
        log.info("Redirecting user {} to {} (isAdmin: {}, roles: {})", authResponse.getUsername(),
                isAdmin ? PathConstants.ADMIN_DASHBOARD : PathConstants.DASHBOARD, isAdmin, authResponse.getRoles());
        return isAdmin ? "redirect:" + PathConstants.ADMIN_DASHBOARD : "redirect:" + PathConstants.DASHBOARD;
    }
}