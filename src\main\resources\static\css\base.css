body {
    font-family: Arial, sans-serif;
    margin: 0 auto;
    padding: 20px;
    background-color: #f5f5f5;
}

.container {
    background-color: white;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    max-width: 800px;
    margin: 0 auto;
}

.container-wide {
    max-width: 1000px;
}

.language-switcher {
    text-align: right;
    margin-bottom: 20px;
}

.language-switcher a {
    color: #007bff;
    text-decoration: none;
}

.language-switcher a:hover {
    text-decoration: underline;
}

.text-center {
    text-align: center;
}

.text-right {
    text-align: right;
}

.mb-20 {
    margin-bottom: 20px;
}

.mt-30 {
    margin-top: 30px;
}

.no-margin {
    margin: 0;
}

.text-muted {
    color: #6c757d;
}

.font-bold {
    font-weight: bold;
}

.empty-state {
    text-align: center;
    padding: 40px;
    background-color: #f8f9fa;
    border-radius: 6px;
    color: #666;
}

.empty-state h4 {
    color: #666;
    margin-bottom: 10px;
}

.empty-state p {
    color: #888;
    margin: 0;
}
