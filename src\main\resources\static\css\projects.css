.project-header {
    padding-bottom: 20px;
    margin-bottom: 30px;
    border-bottom: 2px solid #007bff;
}

.project-header.edit {
    border-bottom-color: #ffc107;
}

.project-header.members {
    border-bottom-color: #17a2b8;
}

.project-title {
    color: #007bff;
    margin: 0 0 10px 0;
}

.project-title.edit {
    color: #ffc107;
}

.project-title.members {
    color: #17a2b8;
}

.section {
    margin-bottom: 40px;
}

.section-title {
    color: #495057;
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 10px;
    margin-bottom: 20px;
}

.project-meta {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.meta-item {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 6px;
    border-left: 4px solid #007bff;
}

.meta-label {
    font-weight: bold;
    color: #495057;
    margin-bottom: 5px;
}

.meta-value {
    color: #6c757d;
}

.status-active { color: #28a745; }
.status-inactive { color: #ffc107; }
.status-completed { color: #17a2b8; }
.status-archived { color: #6c757d; }

.actions-section {
    border-top: 1px solid #dee2e6;
    padding-top: 20px;
    margin-top: 30px;
}
