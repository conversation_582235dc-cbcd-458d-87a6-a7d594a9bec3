package by.algin.webuiservice.service;

import by.algin.dto.response.AuthResponse;
import by.algin.webuiservice.config.AppProperties;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class CookieService {

    private final AppProperties appProperties;

    public void createJwtCookies(AuthResponse authResponse, HttpServletResponse response) {
        if (authResponse == null) {
            log.warn("Cannot create JWT cookies: AuthResponse is null");
            return;
        }

        if (authResponse.getAccessToken() != null) {
            Cookie jwtCookie = createSecureCookie(
                appProperties.getSecurity().getCookies().getJwtCookieName(),
                authResponse.getAccessToken(),
                getTokenExpirySeconds(authResponse.getExpiresIn())
            );
            response.addCookie(jwtCookie);
            log.debug("Access token cookie created for user: {}", authResponse.getUsername());
        }

        if (authResponse.getRefreshToken() != null) {
            Cookie refreshCookie = createSecureCookie(
                appProperties.getSecurity().getCookies().getRefreshJwtCookieName(),
                authResponse.getRefreshToken(),
                appProperties.getSecurity().getJwt().getRefreshToken().getExpirySeconds()
            );
            response.addCookie(refreshCookie);
            log.debug("Refresh token cookie created for user: {}", authResponse.getUsername());
        }

        log.info("JWT cookies successfully created for user: {}", authResponse.getUsername());
    }

    public void clearAuthenticationCookies(HttpServletResponse response) {
        clearCookie(response, appProperties.getSecurity().getCookies().getJwtCookieName());
        clearCookie(response, appProperties.getSecurity().getCookies().getRefreshJwtCookieName());
        clearCookie(response, appProperties.getSecurity().getCookies().getJsessionIdCookieName());

        log.debug("Authentication cookies cleared");
    }

    public Cookie createSecureCookie(String name, String value, int maxAgeSeconds) {
        Cookie cookie = new Cookie(name, value);
        cookie.setHttpOnly(true);
        cookie.setSecure(true);
        cookie.setPath("/");
        cookie.setMaxAge(maxAgeSeconds);

        log.trace("Secure cookie created: name={}, maxAge={}", name, maxAgeSeconds);
        return cookie;
    }

    public void clearCookie(HttpServletResponse response, String cookieName) {
        Cookie cookie = new Cookie(cookieName, null);
        cookie.setPath("/");
        cookie.setHttpOnly(true);
        cookie.setSecure(true);
        cookie.setMaxAge(0);
        response.addCookie(cookie);

        log.trace("Cookie cleared: {}", cookieName);
    }

    private int getTokenExpirySeconds(Long expiresIn) {
        return expiresIn != null
            ? expiresIn.intValue()
            : appProperties.getSecurity().getJwt().getDefaultExpirySeconds();
    }
}
