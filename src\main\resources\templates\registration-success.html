<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title th:text="#{success.registration}">Registration Successful</title>
</head>
<body>
    <div style="text-align: right; margin: 10px;">
        <a th:href="@{/setLanguage(lang='en')}" th:text="#{language.english}">English</a> |
        <a th:href="@{/setLanguage(lang='ru')}" th:text="#{language.russian}">Russian</a>
    </div>

    <h1 th:text="#{success.registration}">Registration Successful</h1>

    <div th:if="${message != null}" style="color: green;">
        <p th:text="${message}"></p>
    </div>

    <div th:if="${message == null}">
        <p th:text="#{registration.check.email}">Please check your email to confirm your account.</p>
    </div>

    <div style="margin-top: 20px;">
        <a th:href="@{/auth/login}" th:text="#{auth.login}">Go to Login</a> |
        <a th:href="@{/}" th:text="#{nav.home}">Return to homepage</a>
    </div>
</body>
</html>