package by.algin.webuiservice.controller;

import by.algin.dto.project.CreateInvitationRequest;
import by.algin.dto.project.InvitationResponse;
import by.algin.dto.project.ProjectRole;
import by.algin.webuiservice.constants.PathConstants;
import by.algin.webuiservice.service.ProjectInvitationWebService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

@Slf4j
@Controller
@RequestMapping(PathConstants.PROJECTS)
@RequiredArgsConstructor
public class ProjectInvitationWebController {
    
    private final ProjectInvitationWebService invitationWebService;

    @GetMapping(PathConstants.PROJECT_INVITE_ENDPOINT)
    public String showInviteForm(@PathVariable String projectId,
                                Authentication authentication, 
                                Model model) {
        
        log.info("Showing invite form for project: {}", projectId);
        
        model.addAttribute("invitation", new CreateInvitationRequest());
        model.addAttribute("projectId", projectId);
        model.addAttribute("roles", ProjectRole.values());
        
        return "projects/invite";
    }

    @PostMapping(PathConstants.PROJECT_INVITE_ENDPOINT)
    public String sendInvitation(@PathVariable String projectId,
                               @Valid @ModelAttribute("invitation") CreateInvitationRequest request,
                               BindingResult result,
                               Authentication authentication,
                               Model model,
                               RedirectAttributes redirectAttributes) {
        
        log.info("Creating invitation for project {} (intended for: {})",
                projectId, request.getEmail() != null ? request.getEmail() : "anyone");
        
        if (result.hasErrors()) {
            log.warn("Validation errors in invitation form: {}", result.getAllErrors());
            model.addAttribute("projectId", projectId);
            model.addAttribute("roles", ProjectRole.values());
            return "projects/invite";
        }
        
        boolean sent = invitationWebService.sendInvitation(projectId, request, authentication, model);
        if (sent) {
            log.info("Invitation sent successfully");
            redirectAttributes.addFlashAttribute("success", "Invitation sent successfully!");
            return "redirect:/projects/" + projectId;
        } else {
            log.error("Failed to send invitation");
            model.addAttribute("projectId", projectId);
            model.addAttribute("roles", ProjectRole.values());
            return "projects/invite";
        }
    }

    @GetMapping(PathConstants.JOIN_ENDPOINT)
    public String showJoinForm(@RequestParam String token,
                              Authentication authentication, 
                              Model model) {
        
        log.info("Showing join form for token: {}", token);
        
        invitationWebService.prepareJoinForm(token, authentication, model);
        return "projects/join";
    }
    
    @PostMapping(PathConstants.JOIN_ENDPOINT)
    public String acceptInvitation(@RequestParam String token,
                                 Authentication authentication,
                                 Model model,
                                 RedirectAttributes redirectAttributes) {
        
        log.info("Accepting invitation with token: {}", token);
        
        boolean accepted = invitationWebService.acceptInvitation(token, authentication, model);
        if (accepted) {
            log.info("Invitation accepted successfully");
            redirectAttributes.addFlashAttribute("success", "Successfully joined the project!");
            return "redirect:" + PathConstants.DASHBOARD;
        } else {
            log.error("Failed to accept invitation");
            return "projects/join";
        }
    }
}
