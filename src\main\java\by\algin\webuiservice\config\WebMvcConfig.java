package by.algin.webuiservice.config;

import by.algin.constants.CommonPathConstants;
import by.algin.constants.CommonTemplateConstants;
import by.algin.webuiservice.constants.PathConstants;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.ViewControllerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        registry.addResourceHandler(CommonTemplateConstants.CSS_RESOURCES)
                .addResourceLocations(CommonTemplateConstants.CSS_LOCATION);
        registry.addResourceHandler(CommonTemplateConstants.JS_RESOURCES)
                .addResourceLocations(CommonTemplateConstants.JS_LOCATION);
    }

    @Override
    public void addViewControllers(ViewControllerRegistry registry) {
        registry.addViewController(PathConstants.ROOT).setViewName(PathConstants.TEMPLATE_INDEX);
        registry.addViewController(CommonPathConstants.AUTH_LOGIN).setViewName(PathConstants.TEMPLATE_LOGIN);
        registry.addViewController(CommonPathConstants.AUTH_REGISTER).setViewName(PathConstants.TEMPLATE_REGISTER);
        registry.addViewController(CommonPathConstants.AUTH_REGISTRATION_SUCCESS).setViewName(PathConstants.TEMPLATE_REGISTRATION_SUCCESS);
        registry.addViewController(PathConstants.DASHBOARD).setViewName(PathConstants.TEMPLATE_DASHBOARD);
        registry.addViewController(PathConstants.ADMIN_DASHBOARD).setViewName(PathConstants.TEMPLATE_ADMIN_DASHBOARD);
        registry.addViewController(CommonPathConstants.AUTH_ACCOUNT_CONFIRMED).setViewName(PathConstants.TEMPLATE_ACCOUNT_CONFIRMED);
    }
}