<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title th:text="'Manage Members: ' + ${project?.name ?: 'Unknown'}">Project Members</title>
    <link rel="stylesheet" th:href="@{/css/base.css}">
    <link rel="stylesheet" th:href="@{/css/forms.css}">
    <link rel="stylesheet" th:href="@{/css/buttons.css}">
    <link rel="stylesheet" th:href="@{/css/alerts.css}">
    <link rel="stylesheet" th:href="@{/css/projects.css}">
    <link rel="stylesheet" th:href="@{/css/members.css}">
</head>
<body>
    <div class="language-switcher">
        <a th:href="@{/setLanguage(lang='en')}" th:text="#{language.english}">English</a> |
        <a th:href="@{/setLanguage(lang='ru')}" th:text="#{language.russian}">Russian</a>
    </div>

    <div class="container container-wide">
        <div th:if="${error}" class="alert alert-danger">
            <p th:text="${error}"></p>
        </div>
        <div th:if="${success}" class="alert alert-success">
            <p th:text="${success}"></p>
        </div>
        <div th:if="${project}" class="project-header members">
            <h1 class="project-title members">Manage Project Members</h1>
            <p class="text-muted no-margin">Project: <strong th:text="${project.name}">Project Name</strong></p>
        </div>


        <div th:unless="${project}" class="alert alert-danger">
            <h2>Project Not Found</h2>
            <p>The requested project could not be found or you don't have permission to manage its members.</p>
            <a th:href="@{/dashboard}" class="btn btn-secondary">Back to Dashboard</a>
        </div>

        <div th:if="${project}">
            <!-- Invitation Section -->
            <div class="section">
                <h3 class="section-title">Invite New Members</h3>
                <div class="invitation-info">
                    <p class="text-muted">Create invitation links that can be shared with anyone to join the project.</p>
                    <a th:href="@{/projects/{id}/invite(id=${project.id})}" class="btn btn-primary">
                        <i class="fas fa-user-plus"></i> Create Invitation Link
                    </a>
                </div>
            </div>

            <div class="section">
                <h3 class="section-title">Add Member Directly</h3>
                <div class="add-member-form">
                    <form th:action="@{/projects/{id}/members(id=${project.id})}" th:object="${memberRequest}" method="post">
                        <input type="hidden" th:name="${_csrf.parameterName}" th:value="${_csrf.token}" />
                        
                        <div class="form-inline">
                            <div class="form-group">
                                <label for="userId">User ID or Username</label>
                                <input type="text" 
                                       id="userId" 
                                       th:field="*{userId}" 
                                       placeholder="Enter user ID or username"
                                       required />
                                <div th:if="${#fields.hasErrors('userId')}" class="error">
                                    <span th:errors="*{userId}"></span>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label for="role">Role</label>
                                <select id="role" th:field="*{role}" required>
                                    <option value="">Select role</option>
                                    <option value="VIEWER">Viewer</option>
                                    <option value="DEVELOPER">Developer</option>
                                    <option value="MANAGER">Manager</option>
                                    <option value="OWNER">Owner</option>
                                </select>
                                <div th:if="${#fields.hasErrors('role')}" class="error">
                                    <span th:errors="*{role}"></span>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <button type="submit" class="btn btn-primary">Add Member</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>


            <div class="section">
                <h3 class="section-title">Current Members</h3>
                
                <div th:if="${members != null and !members.isEmpty()}" class="members-grid wide">
                    <div th:each="member : ${members}" class="member-card detailed">
                        <div class="member-name large" th:text="${member.username ?: member.userId}">Member Name</div>
                        <div class="member-role" th:classappend="'role-' + ${#strings.toLowerCase(member.role)}">
                            Role: <span th:text="${member.role}">Role</span>
                        </div>
                        <div class="member-joined" th:text="'Joined: ' + ${#temporals.format(member.joinedAt, 'dd/MM/yyyy')}">Joined Date</div>
                        
                        <div class="member-actions">

                            <form th:action="@{/projects/{projectId}/members/{userId}/remove(projectId=${project.id}, userId=${member.userId})}" 
                                  method="post" 
                                  style="display: inline;"
                                  th:unless="${member.role == 'OWNER'}"
                                  onsubmit="return confirm('Are you sure you want to remove this member?');">
                                <input type="hidden" th:name="${_csrf.parameterName}" th:value="${_csrf.token}" />
                                <button type="submit" class="btn btn-danger btn-sm">Remove</button>
                            </form>
                            
                            <span th:if="${member.role == 'OWNER'}" class="btn btn-secondary btn-sm" style="cursor: not-allowed;">
                                Cannot Remove Owner
                            </span>
                        </div>
                    </div>
                </div>
                
                <div th:if="${members == null or members.isEmpty()}" class="empty-state">
                    <h4>No Members Found</h4>
                    <p>This project doesn't have any members yet. Add the first member using the form above.</p>
                </div>
            </div>


            <div class="section">
                <a th:href="@{/projects/{id}(id=${project.id})}" class="btn btn-info">Back to Project</a>
                <a th:href="@{/dashboard}" class="btn btn-secondary">Back to Dashboard</a>
            </div>
        </div>
    </div>
</body>
</html>
