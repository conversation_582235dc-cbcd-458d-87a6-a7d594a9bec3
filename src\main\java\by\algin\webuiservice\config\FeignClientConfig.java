package by.algin.webuiservice.config;

import by.algin.constants.CommonPathConstants;
import by.algin.webuiservice.constants.PathConstants;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.Arrays;
import java.util.List;

@Slf4j
@Configuration
public class FeignClientConfig {

    private static final List<String> PUBLIC_ENDPOINTS = List.of(
            CommonPathConstants.LOGIN_ENDPOINT,
            CommonPathConstants.REGISTER_ENDPOINT,
            CommonPathConstants.CONFIRM_ENDPOINT,
            CommonPathConstants.RESEND_CONFIRMATION_ENDPOINT,
            CommonPathConstants.EMAIL_BY_TOKEN_ENDPOINT
    );

    @Bean
    public RequestInterceptor requestTokenInterceptor() {
        return new JwtTokenInterceptor();
    }

    private static class JwtTokenInterceptor implements RequestInterceptor {

        @Override
        public void apply(RequestTemplate template) {
            String url = template.url();
            if (PUBLIC_ENDPOINTS.stream().anyMatch(endpoint -> url.contains(endpoint))) {
                log.debug("Skipping JWT token for public endpoint: {}", url);
                return;
            }

            try {
                String jwtToken = extractJwtTokenFromCurrentRequest();

                if (jwtToken != null) {
                    template.header(CommonPathConstants.AUTHORIZATION_HEADER, CommonPathConstants.BEARER_PREFIX + jwtToken);
                    log.debug("Added JWT token to outgoing request to: {}", url);
                } else {
                    log.warn("No JWT token found for outgoing request to: {}. Request may fail.", url);
                }
            } catch (Exception e) {
                log.warn("Failed to add JWT token to request: {}. Proceeding without token.", e.getMessage());
            }
        }

        private String extractJwtTokenFromCurrentRequest() {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes == null) {
                log.debug("No request attributes available, checking security context");
                return extractTokenFromSecurityContext();
            }

            HttpServletRequest request = attributes.getRequest();
                     
            Cookie[] cookies = request.getCookies();
            if (cookies != null) {
                String token = Arrays.stream(cookies)
                        .filter(cookie -> PathConstants.JWT_COOKIE_NAME.equals(cookie.getName()))
                        .map(Cookie::getValue)
                        .findFirst()
                        .orElse(null);
                if (token != null) {
                    return token;
                }
            }

            String header = request.getHeader(CommonPathConstants.AUTHORIZATION_HEADER);
            if (header != null && header.startsWith(CommonPathConstants.BEARER_PREFIX)) {
                return header.substring(CommonPathConstants.BEARER_PREFIX.length());
            }

            log.debug("No token found in cookies or header, checking security context");
            return extractTokenFromSecurityContext();
        }

        private String extractTokenFromSecurityContext() {
            var authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null && authentication.getCredentials() instanceof String) {
                String token = (String) authentication.getCredentials();
                if (token.startsWith(CommonPathConstants.BEARER_PREFIX)) {
                    return token.substring(CommonPathConstants.BEARER_PREFIX.length());
                }
            }
            return null;
        }
    }
}