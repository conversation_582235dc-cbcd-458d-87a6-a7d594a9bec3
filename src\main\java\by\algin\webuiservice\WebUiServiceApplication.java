package by.algin.webuiservice;

import by.algin.webuiservice.config.AppProperties;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;

@EnableDiscoveryClient
@EnableFeignClients
@SpringBootApplication
@EnableConfigurationProperties(AppProperties.class)
public class WebUiServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(WebUiServiceApplication.class, args);
    }

}
