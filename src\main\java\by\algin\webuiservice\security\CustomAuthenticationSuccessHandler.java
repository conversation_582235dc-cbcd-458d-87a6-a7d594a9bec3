package by.algin.webuiservice.security;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.AuthenticationSuccessHandler;
import org.springframework.stereotype.Component;

import by.algin.webuiservice.constants.PathConstants;
import by.algin.webuiservice.constants.RoleConstants;

import java.io.IOException;

@Component
public class CustomAuthenticationSuccessHandler implements AuthenticationSuccessHandler {

    @Override
    public void onAuthenticationSuccess(HttpServletRequest request, HttpServletResponse response,
                                        Authentication authentication) throws IOException {
        String redirectUrl = authentication.getAuthorities().stream()
                .anyMatch(auth -> RoleConstants.ADMIN.equals(auth.getAuthority()))
                ? PathConstants.ADMIN_DASHBOARD
                : PathConstants.DASHBOARD;
        response.sendRedirect(redirectUrl);
    }
}