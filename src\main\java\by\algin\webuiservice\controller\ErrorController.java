package by.algin.webuiservice.controller;

import by.algin.constants.CommonTemplateConstants;
import by.algin.webuiservice.constants.PathConstants;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;

import jakarta.servlet.http.HttpServletRequest;

@Slf4j
@Controller
public class ErrorController implements org.springframework.boot.web.servlet.error.ErrorController {

    @RequestMapping(PathConstants.ERROR)
    public String handleError(HttpServletRequest request, Model model) {
        Integer statusCode = (Integer) request.getAttribute("jakarta.servlet.error.status_code");
        String errorMessage = (String) request.getAttribute("jakarta.servlet.error.message");
        
        log.error("Error occurred: status={}, message={}", statusCode, errorMessage);
        
        model.addAttribute("error", "An error occurred. Please try again.");
        model.addAttribute("statusCode", statusCode);
        
        return CommonTemplateConstants.TEMPLATE_ERROR;
    }
}
